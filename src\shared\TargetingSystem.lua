--[[
	TargetingSystem - 優化的目標搜尋系統
	提供高效的怪物目標搜尋和緩存機制
]]

local TargetingSystem = {}
local DebugConfig = require(script.Parent.DebugConfig)

-- 私有變量
local monsterCache = {} -- 怪物位置緩存
local lastCacheUpdate = 0
local CACHE_UPDATE_INTERVAL = 0.1 -- 每0.1秒更新一次緩存
local MAX_SEARCH_DISTANCE = 50 -- 最大搜尋距離

-- 距離平方計算（避免開方運算）
local function getDistanceSquared(pos1, pos2)
	local dx = pos1.X - pos2.X
	local dy = pos1.Y - pos2.Y
	local dz = pos1.Z - pos2.Z
	return dx * dx + dy * dy + dz * dz
end

-- 更新怪物緩存
function TargetingSystem.updateMonsterCache()
	local currentTime = tick()
	if currentTime - lastCacheUpdate < CACHE_UPDATE_INTERVAL then
		return -- 還沒到更新時間
	end
	
	lastCacheUpdate = currentTime
	monsterCache = {} -- 清空舊緩存
	
	-- 遍歷 workspace 中的怪物
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") and model:FindFirstChild("HumanoidRootPart") then
			local instanceId = nil
			local parts = string.split(model.Name, "_")
			if #parts >= 2 then
				instanceId = parts[2]
			end
			
			if instanceId then
				monsterCache[instanceId] = {
					model = model,
					position = model.HumanoidRootPart.Position,
					lastUpdate = currentTime
				}
			end
		end
	end
	
	-- 使用調試配置系統輸出信息
	local monsterCount = 0
	for _ in pairs(monsterCache) do
		monsterCount = monsterCount + 1
	end

	-- 只在怪物數量變化時輸出，或使用間隔輸出
	if not TargetingSystem._lastMonsterCount then
		TargetingSystem._lastMonsterCount = 0
	end

	if monsterCount ~= TargetingSystem._lastMonsterCount then
		TargetingSystem._lastMonsterCount = monsterCount
		DebugConfig.log("targeting", "🎯 Monster cache updated:", monsterCount, "monsters")
	else
		-- 使用間隔輸出避免刷屏
		DebugConfig.logWithInterval("targeting", "cache_stats", currentTime,
			"🎯 Monster cache stats:", monsterCount, "monsters")
	end
end

-- 尋找最近的怪物（優化版本）
function TargetingSystem.findNearestMonster(playerPosition, maxDistance)
	if not playerPosition then
		return nil
	end
	
	maxDistance = maxDistance or MAX_SEARCH_DISTANCE
	local maxDistanceSquared = maxDistance * maxDistance
	
	-- 確保緩存是最新的
	TargetingSystem.updateMonsterCache()
	
	local nearestMonster = nil
	local nearestDistanceSquared = math.huge
	
	-- 在緩存中搜尋
	for instanceId, monsterData in pairs(monsterCache) do
		-- 檢查怪物是否仍然存在
		if monsterData.model and monsterData.model.Parent then
			local distanceSquared = getDistanceSquared(playerPosition, monsterData.position)
			
			-- 只考慮在搜尋範圍內的怪物
			if distanceSquared <= maxDistanceSquared and distanceSquared < nearestDistanceSquared then
				nearestDistanceSquared = distanceSquared
				nearestMonster = instanceId
			end
		else
			-- 清理無效的緩存條目
			monsterCache[instanceId] = nil
		end
	end
	
	return nearestMonster, math.sqrt(nearestDistanceSquared)
end

-- 獲取怪物位置（從緩存）
function TargetingSystem.getMonsterPosition(instanceId)
	local monsterData = monsterCache[instanceId]
	if monsterData and monsterData.model and monsterData.model.Parent then
		return monsterData.position
	end
	return nil
end

-- 檢查怪物是否存在
function TargetingSystem.isMonsterValid(instanceId)
	local monsterData = monsterCache[instanceId]
	return monsterData and monsterData.model and monsterData.model.Parent ~= nil
end

-- 獲取範圍內的所有怪物
function TargetingSystem.getMonstersInRange(playerPosition, range)
	if not playerPosition then
		return {}
	end
	
	local rangeSquared = range * range
	local monstersInRange = {}
	
	TargetingSystem.updateMonsterCache()
	
	for instanceId, monsterData in pairs(monsterCache) do
		if monsterData.model and monsterData.model.Parent then
			local distanceSquared = getDistanceSquared(playerPosition, monsterData.position)
			if distanceSquared <= rangeSquared then
				table.insert(monstersInRange, {
					instanceId = instanceId,
					distance = math.sqrt(distanceSquared),
					position = monsterData.position
				})
			end
		end
	end
	
	-- 按距離排序
	table.sort(monstersInRange, function(a, b)
		return a.distance < b.distance
	end)
	
	return monstersInRange
end

-- 清理緩存
function TargetingSystem.clearCache()
	monsterCache = {}
	lastCacheUpdate = 0
end

-- 獲取緩存統計信息
function TargetingSystem.getCacheStats()
	return {
		monsterCount = 0,
		lastUpdate = lastCacheUpdate,
		cacheAge = tick() - lastCacheUpdate
	}
end

-- 手動移除怪物從緩存
function TargetingSystem.removeMonsterFromCache(instanceId)
	if monsterCache[instanceId] then
		monsterCache[instanceId] = nil
		print("🎯 Removed monster from cache:", instanceId)
	end
end

-- 設置緩存更新間隔
function TargetingSystem.setCacheUpdateInterval(interval)
	CACHE_UPDATE_INTERVAL = math.max(0.05, interval) -- 最小0.05秒
end

-- 設置最大搜尋距離
function TargetingSystem.setMaxSearchDistance(distance)
	MAX_SEARCH_DISTANCE = math.max(10, distance) -- 最小10格
end

return TargetingSystem
