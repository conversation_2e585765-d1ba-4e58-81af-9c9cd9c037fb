--[[
	MonsterHealthUI - 怪物血量UI系統
	為怪物創建和管理血量條顯示
]]

local MonsterHealthUI = {}

local TweenService = game:GetService("TweenService")

-- 創建怪物血量條
function MonsterHealthUI.createHealthBar(monster, maxHealth, currentHealth)
	if not monster or not monster:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart") then
		warn("MonsterHealthUI: Invalid monster or missing HumanoidRootPart")
		return nil
	end
	
	-- 檢查是否已經有血量條
	local existingHealthBar = monster:FindFirstChild("HealthBarGui")
	if existingHealthBar then
		existingHealthBar:Destroy()
	end
	
	-- 創建 BillboardGui
	local healthBarGui = Instance.new("BillboardGui")
	healthBarGui.Name = "HealthBarGui"
	healthBarGui.Size = UDim2.new(0, 100, 0, 12)
	healthBarGui.StudsOffset = Vector3.new(0, 3, 0) -- 在怪物頭頂上方3格
	healthBarGui.Adornee = monster.HumanoidRootPart
	healthBarGui.Parent = monster
	
	-- 背景框架
	local backgroundFrame = Instance.new("Frame")
	backgroundFrame.Name = "Background"
	backgroundFrame.Size = UDim2.new(1, 0, 1, 0)
	backgroundFrame.Position = UDim2.new(0, 0, 0, 0)
	backgroundFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	backgroundFrame.BorderSizePixel = 1
	backgroundFrame.BorderColor3 = Color3.fromRGB(255, 255, 255)
	backgroundFrame.Parent = healthBarGui
	
	-- 血量條
	local healthBar = Instance.new("Frame")
	healthBar.Name = "HealthBar"
	healthBar.Size = UDim2.new(currentHealth / maxHealth, 0, 1, 0)
	healthBar.Position = UDim2.new(0, 0, 0, 0)
	healthBar.BackgroundColor3 = MonsterHealthUI._getHealthColor(currentHealth / maxHealth)
	healthBar.BorderSizePixel = 0
	healthBar.Parent = backgroundFrame
	
	-- 血量文字
	local healthText = Instance.new("TextLabel")
	healthText.Name = "HealthText"
	healthText.Size = UDim2.new(1, 0, 1, 0)
	healthText.Position = UDim2.new(0, 0, 0, 0)
	healthText.BackgroundTransparency = 1
	healthText.Text = currentHealth .. "/" .. maxHealth
	healthText.TextColor3 = Color3.fromRGB(255, 255, 255)
	healthText.TextScaled = true
	healthText.Font = Enum.Font.GothamBold
	healthText.TextStrokeTransparency = 0
	healthText.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
	healthText.Parent = backgroundFrame
	
	-- 存儲血量數據
	healthBarGui:SetAttribute("MaxHealth", maxHealth)
	healthBarGui:SetAttribute("CurrentHealth", currentHealth)
	
	print("💚 Health bar created for monster:", monster.Name)
	return healthBarGui
end

-- 更新怪物血量條
function MonsterHealthUI.updateHealthBar(monster, newHealth, maxHealth, animated)
	if not monster then return end
	
	local healthBarGui = monster:FindFirstChild("HealthBarGui")
	if not healthBarGui then
		-- 如果沒有血量條，創建一個
		return MonsterHealthUI.createHealthBar(monster, maxHealth, newHealth)
	end
	
	local backgroundFrame = healthBarGui:FindFirstChild("Background")
	local healthBar = backgroundFrame and backgroundFrame:FindFirstChild("HealthBar")
	local healthText = backgroundFrame and backgroundFrame:FindFirstChild("HealthText")
	
	if not healthBar or not healthText then
		warn("MonsterHealthUI: Health bar components not found")
		return
	end
	
	-- 計算血量比例
	local healthRatio = math.max(0, math.min(1, newHealth / maxHealth))
	
	-- 更新顏色
	local newColor = MonsterHealthUI._getHealthColor(healthRatio)
	
	-- 更新文字
	healthText.Text = math.ceil(newHealth) .. "/" .. maxHealth
	
	if animated then
		-- 動畫更新血量條
		local sizeTween = TweenService:Create(
			healthBar,
			TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				Size = UDim2.new(healthRatio, 0, 1, 0),
				BackgroundColor3 = newColor
			}
		)
		sizeTween:Play()
		
		-- 如果血量降低，添加受傷效果
		local currentHealth = healthBarGui:GetAttribute("CurrentHealth") or maxHealth
		if newHealth < currentHealth then
			MonsterHealthUI._playDamageEffect(healthBarGui)
		end
	else
		-- 直接更新
		healthBar.Size = UDim2.new(healthRatio, 0, 1, 0)
		healthBar.BackgroundColor3 = newColor
	end
	
	-- 更新存儲的血量數據
	healthBarGui:SetAttribute("CurrentHealth", newHealth)
	healthBarGui:SetAttribute("MaxHealth", maxHealth)
	
	-- 如果血量為0，播放死亡效果
	if newHealth <= 0 then
		MonsterHealthUI._playDeathEffect(healthBarGui)
	end
end

-- 移除怪物血量條
function MonsterHealthUI.removeHealthBar(monster)
	if not monster then return end
	
	local healthBarGui = monster:FindFirstChild("HealthBarGui")
	if healthBarGui then
		-- 淡出動畫
		local fadeOut = TweenService:Create(
			healthBarGui,
			TweenInfo.new(0.5, Enum.EasingStyle.Quad),
			{StudsOffset = Vector3.new(0, 5, 0)}
		)
		
		local transparencyTween = TweenService:Create(
			healthBarGui:FindFirstChild("Background"),
			TweenInfo.new(0.5, Enum.EasingStyle.Quad),
			{BackgroundTransparency = 1}
		)
		
		fadeOut:Play()
		transparencyTween:Play()
		
		fadeOut.Completed:Connect(function()
			healthBarGui:Destroy()
		end)
		
		print("💚 Health bar removed for monster:", monster.Name)
	end
end

-- 根據血量比例獲取顏色
function MonsterHealthUI._getHealthColor(healthRatio)
	if healthRatio > 0.6 then
		-- 綠色 (健康)
		return Color3.fromRGB(100, 255, 100)
	elseif healthRatio > 0.3 then
		-- 黃色 (受傷)
		return Color3.fromRGB(255, 255, 100)
	else
		-- 紅色 (危險)
		return Color3.fromRGB(255, 100, 100)
	end
end

-- 播放受傷效果
function MonsterHealthUI._playDamageEffect(healthBarGui)
	local backgroundFrame = healthBarGui:FindFirstChild("Background")
	if not backgroundFrame then return end
	
	-- 震動效果
	local originalPosition = backgroundFrame.Position
	
	for i = 1, 3 do
		local shake = TweenService:Create(
			backgroundFrame,
			TweenInfo.new(0.05, Enum.EasingStyle.Quad),
			{Position = originalPosition + UDim2.new(0, math.random(-2, 2), 0, math.random(-1, 1))}
		)
		shake:Play()
		shake.Completed:Wait()
	end
	
	-- 恢復原位
	backgroundFrame.Position = originalPosition
	
	-- 紅色閃爍
	local healthBar = backgroundFrame:FindFirstChild("HealthBar")
	if healthBar then
		local originalColor = healthBar.BackgroundColor3
		healthBar.BackgroundColor3 = Color3.fromRGB(255, 0, 0)
		
		local colorTween = TweenService:Create(
			healthBar,
			TweenInfo.new(0.2, Enum.EasingStyle.Quad),
			{BackgroundColor3 = originalColor}
		)
		colorTween:Play()
	end
end

-- 播放死亡效果
function MonsterHealthUI._playDeathEffect(healthBarGui)
	local backgroundFrame = healthBarGui:FindFirstChild("Background")
	if not backgroundFrame then return end
	
	-- 血量條變黑
	local healthBar = backgroundFrame:FindFirstChild("HealthBar")
	if healthBar then
		local deathTween = TweenService:Create(
			healthBar,
			TweenInfo.new(0.5, Enum.EasingStyle.Quad),
			{BackgroundColor3 = Color3.fromRGB(100, 100, 100)}
		)
		deathTween:Play()
	end
	
	-- 文字變為 "DEAD"
	local healthText = backgroundFrame:FindFirstChild("HealthText")
	if healthText then
		healthText.Text = "DEAD"
		healthText.TextColor3 = Color3.fromRGB(255, 0, 0)
	end
end

-- 批量更新所有怪物血量條
function MonsterHealthUI.updateAllHealthBars()
	for _, model in pairs(workspace:GetChildren()) do
		if model:IsA("Model") and model:FindFirstChild("MonsterId") and model:FindFirstChild("Humanoid") then
			local humanoid = model.Humanoid
			local maxHealth = humanoid.MaxHealth
			local currentHealth = humanoid.Health
			
			MonsterHealthUI.updateHealthBar(model, currentHealth, maxHealth, false)
		end
	end
end

-- 設置血量條可見性
function MonsterHealthUI.setHealthBarVisible(monster, visible)
	if not monster then return end
	
	local healthBarGui = monster:FindFirstChild("HealthBarGui")
	if healthBarGui then
		healthBarGui.Enabled = visible
	end
end

-- 獲取怪物當前血量比例
function MonsterHealthUI.getHealthRatio(monster)
	if not monster then return 0 end
	
	local healthBarGui = monster:FindFirstChild("HealthBarGui")
	if healthBarGui then
		local currentHealth = healthBarGui:GetAttribute("CurrentHealth") or 0
		local maxHealth = healthBarGui:GetAttribute("MaxHealth") or 1
		return currentHealth / maxHealth
	end
	
	return 0
end

return MonsterHealthUI
