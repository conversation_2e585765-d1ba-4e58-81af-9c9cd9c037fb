--[[
	ECS Components - Matter ECS 組件定義
	定義所有遊戲實體的組件
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)

-- 位置組件
local PositionComponent = Matter.component("Position", {
	position = Vector3.new(0, 0, 0),
	rotation = CFrame.new(),
})

-- 健康組件
local HealthComponent = Matter.component("Health", {
	maxHealth = 100,
	currentHealth = 100,
	isDead = false,
})

-- 傷害組件
local DamageComponent = Matter.component("Damage", {
	amount = 10,
	damageType = "physical", -- physical, magic, poison
	source = nil, -- 傷害來源實體
})

-- 寵物組件
local PetComponent = Matter.component("Pet", {
	petId = "",
	ownerId = 0, -- 玩家 UserId
	level = 1,
	experience = 0,
	rarity = "common", -- common, rare, epic, legendary
	isActive = true,
})

-- 跟隨目標組件
local FollowTargetComponent = Matter.component("FollowTarget", {
	targetId = nil, -- 目標實體 ID
	followDistance = 5,
	speed = 16,
	isFollowing = true,
})

-- 揮劍組件
local SwordSwingComponent = Matter.component("SwordSwing", {
	isSwinging = false,
	swingDuration = 0.5,
	swingStartTime = 0,
	damage = 20,
	range = 10,
})

-- 目標組件
local TargetComponent = Matter.component("Target", {
	targetId = nil, -- 攻擊目標實體 ID
	attackRange = 8,
	lastAttackTime = 0,
	attackCooldown = 1.5,
})

-- 玩家組件
local PlayerComponent = Matter.component("Player", {
	userId = 0,
	playerName = "",
	coins = 0,
	level = 1,
	experience = 0,
})

-- 怪物組件
local MonsterComponent = Matter.component("Monster", {
	monsterId = "",
	aiType = "patrol", -- patrol, aggressive, passive
	patrolRadius = 20,
	detectionRange = 15,
	attackDamage = 15,
	dropRewards = {},
})

-- 武器組件
local WeaponComponent = Matter.component("Weapon", {
	weaponId = "",
	damage = 20,
	attackSpeed = 1.0,
	range = 10,
	specialEffect = nil,
})

-- 移動組件
local MovementComponent = Matter.component("Movement", {
	velocity = Vector3.new(0, 0, 0),
	speed = 16,
	acceleration = 50,
	isMoving = false,
})

-- 渲染組件（用於 3D 模型）
local RenderComponent = Matter.component("Render", {
	model = nil, -- Roblox Model 實例
	primaryPart = nil,
	animations = {},
})

-- 生命週期組件
local LifetimeComponent = Matter.component("Lifetime", {
	maxLifetime = 60, -- 秒
	currentLifetime = 0,
	shouldDestroy = false,
})

-- 區域組件（用於戰鬥區域檢測）
local ZoneComponent = Matter.component("Zone", {
	zoneType = "combat", -- combat, safe, special
	radius = 50,
	isActive = true,
	playersInZone = {},
})

-- 特效組件
local EffectComponent = Matter.component("Effect", {
	effectType = "particle", -- particle, sound, animation
	duration = 2.0,
	startTime = 0,
	isPlaying = false,
})

-- 輸入組件（玩家輸入）
local InputComponent = Matter.component("Input", {
	isAttacking = false,
	movementDirection = Vector3.new(0, 0, 0),
	lastInputTime = 0,
})

-- AI 組件
local AIComponent = Matter.component("AI", {
	state = "idle", -- idle, patrol, chase, attack, return
	targetPosition = Vector3.new(0, 0, 0),
	homePosition = Vector3.new(0, 0, 0),
	stateChangeTime = 0,
})

-- 導出所有組件
return {
	Position = PositionComponent,
	Health = HealthComponent,
	Damage = DamageComponent,
	Pet = PetComponent,
	FollowTarget = FollowTargetComponent,
	SwordSwing = SwordSwingComponent,
	Target = TargetComponent,
	Player = PlayerComponent,
	Monster = MonsterComponent,
	Weapon = WeaponComponent,
	Movement = MovementComponent,
	Render = RenderComponent,
	Lifetime = LifetimeComponent,
	Zone = ZoneComponent,
	Effect = EffectComponent,
	Input = InputComponent,
	AI = AIComponent,
}
