--[[
	EntityService - 實體管理服務
	負責管理所有 ECS 實體的創建、銷毀和 ID 映射
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
local Players = game:GetService("Players")

local EntityService = Knit.CreateService({
	Name = "EntityService",
	Client = {},
})

-- 私有變量
local playerEntities = {} -- {[player] = entityId}
local entityToPlayer = {} -- {[entityId] = player}
local monsterEntities = {} -- {[instanceId] = entityId}
local petEntities = {} -- {[player] = {[petId] = entityId}}

function EntityService:KnitStart()
	print("🎯 EntityService started")
	
	-- 監聽玩家加入
	Players.PlayerAdded:Connect(function(player)
		-- 等待角色生成
		if player.Character then
			self:_createPlayerEntity(player)
		end
		
		player.CharacterAdded:Connect(function(character)
			self:_createPlayerEntity(player)
		end)
		
		player.CharacterRemoving:Connect(function(character)
			self:_destroyPlayerEntity(player)
		end)
	end)
	
	-- 監聽玩家離開
	Players.PlayerRemoving:Connect(function(player)
		self:_cleanupPlayerEntities(player)
	end)
	
	-- 為已存在的玩家創建實體
	for _, player in pairs(Players:GetPlayers()) do
		if player.Character then
			self:_createPlayerEntity(player)
		end
	end
end

function EntityService:KnitInit()
	-- 獲取其他服務
	self.DataService = Knit.GetService("DataService")
end

-- 創建玩家實體
function EntityService:_createPlayerEntity(player)
	local world = _G.ECS_WORLD
	if not world then
		warn("❌ ECS World not found")
		return
	end
	
	local character = player.Character
	if not character or not character.PrimaryPart then
		warn("❌ Player character not ready:", player.Name)
		return
	end
	
	-- 如果玩家已有實體，先銷毀
	if playerEntities[player] then
		self:_destroyPlayerEntity(player)
	end
	
	-- 獲取玩家數據
	local playerData = self.DataService:GetPlayerData(player)
	local maxHealth = 100
	local currentHealth = playerData and playerData.health or maxHealth
	
	-- 創建玩家實體
	local playerEntity = world:spawn(
		Components.Player({
			userId = player.UserId,
			name = player.Name,
			level = playerData and playerData.level or 1,
			experience = playerData and playerData.experience or 0,
			coins = playerData and playerData.coins or 0,
		}),
		Components.Health({
			maxHealth = maxHealth,
			currentHealth = currentHealth,
			isDead = false,
		}),
		Components.Position({
			position = character.PrimaryPart.Position,
			rotation = character.PrimaryPart.CFrame,
		}),
		Components.Render({
			model = character,
			primaryPart = character.PrimaryPart,
		}),
		Components.Movement({
			velocity = Vector3.new(0, 0, 0),
			speed = 16,
			isMoving = false,
		}),
		Components.Input({
			keys = {},
			mousePosition = Vector3.new(0, 0, 0),
			isInputEnabled = true,
		})
	)
	
	-- 記錄映射關係
	playerEntities[player] = playerEntity
	entityToPlayer[playerEntity] = player
	
	-- 初始化寵物實體表
	if not petEntities[player] then
		petEntities[player] = {}
	end
	
	print(string.format("🎯 Created player entity for %s (ID: %s)", player.Name, tostring(playerEntity)))
	
	return playerEntity
end

-- 銷毀玩家實體
function EntityService:_destroyPlayerEntity(player)
	local world = _G.ECS_WORLD
	if not world then return end
	
	local playerEntity = playerEntities[player]
	if playerEntity then
		-- 銷毀實體
		world:despawn(playerEntity)
		
		-- 清除映射
		entityToPlayer[playerEntity] = nil
		playerEntities[player] = nil
		
		print(string.format("🎯 Destroyed player entity for %s", player.Name))
	end
end

-- 清理玩家所有實體
function EntityService:_cleanupPlayerEntities(player)
	-- 銷毀玩家實體
	self:_destroyPlayerEntity(player)
	
	-- 銷毀玩家的寵物實體
	if petEntities[player] then
		for petId, petEntity in pairs(petEntities[player]) do
			self:DestroyPetEntity(player, petId)
		end
		petEntities[player] = nil
	end
	
	print(string.format("🎯 Cleaned up all entities for %s", player.Name))
end

-- 獲取玩家實體 ID
function EntityService:GetPlayerEntityId(player)
	return playerEntities[player]
end

-- 根據實體 ID 獲取玩家
function EntityService:GetPlayerByEntityId(entityId)
	return entityToPlayer[entityId]
end

-- 創建寵物實體
function EntityService:CreatePetEntity(player, petId, petData, level, experience, rarity)
	local world = _G.ECS_WORLD
	if not world then
		warn("❌ ECS World not found")
		return nil
	end
	
	local playerEntity = playerEntities[player]
	if not playerEntity then
		warn("❌ Player entity not found for:", player.Name)
		return nil
	end
	
	local character = player.Character
	if not character or not character.PrimaryPart then
		warn("❌ Player character not ready:", player.Name)
		return nil
	end
	
	-- 計算寵物生成位置
	local spawnPosition = character.PrimaryPart.Position + Vector3.new(5, 0, 0)
	
	-- 創建寵物實體
	local petEntity = world:spawn(
		Components.Pet({
			petId = petId,
			ownerId = player.UserId,
			level = level or 1,
			experience = experience or 0,
			rarity = rarity or "common",
			isActive = true,
		}),
		Components.Health({
			maxHealth = petData.baseHealth or 100,
			currentHealth = petData.baseHealth or 100,
			isDead = false,
		}),
		Components.Position({
			position = spawnPosition,
			rotation = CFrame.new(),
		}),
		Components.FollowTarget({
			targetId = playerEntity,
			followDistance = 5,
			speed = 16,
			isFollowing = true,
		}),
		Components.Lifetime({
			maxLifetime = 300, -- 5分鐘
			currentLifetime = 0,
		})
	)
	
	-- 記錄寵物實體
	if not petEntities[player] then
		petEntities[player] = {}
	end
	petEntities[player][petId] = petEntity
	
	print(string.format("🎯 Created pet entity %s for %s (ID: %s)", petId, player.Name, tostring(petEntity)))
	
	return petEntity
end

-- 銷毀寵物實體
function EntityService:DestroyPetEntity(player, petId)
	local world = _G.ECS_WORLD
	if not world then return end
	
	if petEntities[player] and petEntities[player][petId] then
		local petEntity = petEntities[player][petId]
		
		-- 銷毀實體
		world:despawn(petEntity)
		
		-- 清除記錄
		petEntities[player][petId] = nil
		
		print(string.format("🎯 Destroyed pet entity %s for %s", petId, player.Name))
	end
end

-- 獲取寵物實體 ID
function EntityService:GetPetEntityId(player, petId)
	if petEntities[player] then
		return petEntities[player][petId]
	end
	return nil
end

-- 創建怪物實體
function EntityService:CreateMonsterEntity(monsterId, position, level, health, attack)
	local world = _G.ECS_WORLD
	if not world then
		warn("❌ ECS World not found")
		return nil
	end
	
	-- 生成唯一實例 ID
	local instanceId = monsterId .. "_" .. tostring(tick())
	
	-- 創建怪物實體
	local monsterEntity = world:spawn(
		Components.Monster({
			monsterId = monsterId,
			instanceId = instanceId,
			level = level or 1,
			experienceReward = level * 10,
			coinReward = level * 5,
		}),
		Components.Health({
			maxHealth = health or 50,
			currentHealth = health or 50,
			isDead = false,
		}),
		Components.Position({
			position = position,
			rotation = CFrame.new(),
		}),
		Components.AI({
			state = "idle",
			targetId = nil,
			detectionRange = 20,
			attackRange = 5,
			patrolRadius = 10,
			lastStateChange = 0,
		}),
		Components.Movement({
			velocity = Vector3.new(0, 0, 0),
			speed = 12,
			isMoving = false,
		}),
		Components.Damage({
			amount = attack or 15,
			damageType = "physical",
			lastDamageTime = 0,
		})
	)
	
	-- 記錄怪物實體
	monsterEntities[instanceId] = monsterEntity
	
	print(string.format("🎯 Created monster entity %s at %s (ID: %s)", 
		monsterId, tostring(position), tostring(monsterEntity)))
	
	return monsterEntity, instanceId
end

-- 銷毀怪物實體
function EntityService:DestroyMonsterEntity(instanceId)
	local world = _G.ECS_WORLD
	if not world then return end
	
	local monsterEntity = monsterEntities[instanceId]
	if monsterEntity then
		-- 銷毀實體
		world:despawn(monsterEntity)
		
		-- 清除記錄
		monsterEntities[instanceId] = nil
		
		print(string.format("🎯 Destroyed monster entity %s", instanceId))
	end
end

-- 獲取怪物實體 ID
function EntityService:GetMonsterEntityId(instanceId)
	return monsterEntities[instanceId]
end

-- 獲取所有玩家實體
function EntityService:GetAllPlayerEntities()
	local entities = {}
	for player, entityId in pairs(playerEntities) do
		table.insert(entities, {
			player = player,
			entityId = entityId,
		})
	end
	return entities
end

-- 獲取所有怪物實體
function EntityService:GetAllMonsterEntities()
	local entities = {}
	for instanceId, entityId in pairs(monsterEntities) do
		table.insert(entities, {
			instanceId = instanceId,
			entityId = entityId,
		})
	end
	return entities
end

-- 更新玩家位置組件
function EntityService:UpdatePlayerPosition(player)
	local world = _G.ECS_WORLD
	if not world then return end
	
	local playerEntity = playerEntities[player]
	local character = player.Character
	
	if playerEntity and character and character.PrimaryPart then
		world:insert(playerEntity, Components.Position({
			position = character.PrimaryPart.Position,
			rotation = character.PrimaryPart.CFrame,
		}))
	end
end

return EntityService
