--[[
	Fusion 0.2 API 測試
	用於驗證正確的 Fusion 0.2 API 使用方法
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

return function()
	describe("Fusion 0.2 API", function()
		it("should load Fusion correctly", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			expect(Fusion).to.be.ok()
			
			-- 檢查 Fusion 0.2 的正確 API
			print("=== Fusion 0.2 Available APIs ===")
			for key, value in pairs(Fusion) do
				print(key, ":", typeof(value))
			end
			print("================================")
		end)
		
		it("should create Value state objects", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			
			-- 在 Fusion 0.2 中，State 可能叫做 Value
			local Value = Fusion.Value
			expect(Value).to.be.ok()
			
			local myValue = Value("test")
			expect(myValue).to.be.ok()
			expect(myValue:get()).to.equal("test")
			
			myValue:set("new value")
			expect(myValue:get()).to.equal("new value")
		end)
		
		it("should create Computed state objects", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			
			local Value = Fusion.Value
			local Computed = Fusion.Computed
			expect(Computed).to.be.ok()
			
			local baseValue = Value(10)
			local computedValue = Computed(function()
				return baseValue:get() * 2
			end)
			
			expect(computedValue:get()).to.equal(20)
			
			baseValue:set(15)
			expect(computedValue:get()).to.equal(30)
		end)
		
		it("should create UI with New", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			
			local New = Fusion.New
			expect(New).to.be.ok()
			
			-- 測試 New 的正確語法
			local frame = New "Frame" {
				Name = "TestFrame",
				Size = UDim2.new(0, 100, 0, 100),
			}
			
			expect(frame).to.be.ok()
			expect(frame.Name).to.equal("TestFrame")
			expect(frame.Size).to.equal(UDim2.new(0, 100, 0, 100))
		end)
		
		it("should handle Children correctly", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			
			local New = Fusion.New
			local Children = Fusion.Children
			expect(Children).to.be.ok()
			
			local parent = New "Frame" {
				Name = "Parent",
				
				[Children] = {
					New "Frame" {
						Name = "Child1",
					},
					New "Frame" {
						Name = "Child2",
					},
				},
			}
			
			expect(parent).to.be.ok()
			expect(#parent:GetChildren()).to.equal(2)
		end)
		
		it("should handle OnEvent correctly", function()
			local Fusion = require(ReplicatedStorage.Packages.fusion)
			
			local New = Fusion.New
			local OnEvent = Fusion.OnEvent
			expect(OnEvent).to.be.ok()
			
			local clicked = false
			local button = New "TextButton" {
				Name = "TestButton",
				
				[OnEvent "Activated"] = function()
					clicked = true
				end,
			}
			
			expect(button).to.be.ok()
			-- 模擬點擊
			button.Activated:Fire()
			expect(clicked).to.equal(true)
		end)
	end)
end
