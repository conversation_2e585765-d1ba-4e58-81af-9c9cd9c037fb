local Font = import("../Enum/Font")
local Instance = import("../Instance")
local TextXAlignment = import("../Enum/TextXAlignment")
local TextYAlignment = import("../Enum/TextYAlignment")
local typeof = import("../functions/typeof")

describe("instances.TextButton", function()
	it("should instantiate", function()
		local instance = Instance.new("TextButton")

		assert.not_nil(instance)
	end)

	it("should have properties defined", function()
		local instance = Instance.new("TextButton")
		assert.equal(typeof(instance.Font), "EnumItem")
		assert.equal(instance.Font.EnumType, Font)
		assert.equal(typeof(instance.Text), "string")
		assert.equal(typeof(instance.TextColor3), "Color3")
		assert.equal(typeof(instance.TextSize), "number")
		assert.equal(typeof(instance.TextWrapped), "boolean")
		assert.equal(typeof(instance.TextXAlignment), "EnumItem")
		assert.equal(instance.TextXAlignment.EnumType, TextXAlignment)
		assert.equal(typeof(instance.TextYAlignment), "EnumItem")
		assert.equal(instance.TextYAlignment.EnumType, TextYAlignment)
	end)
end)
