--[[
	LifetimeSystem - 生命週期系統
	處理實體的生命週期，包括召喚獸的存活時間控制
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
local RunService = game:GetService("RunService")

local function lifetimeSystem(world)
	-- 查詢所有有生命週期組件的實體
	for entityId, lifetime in world:query(Components.Lifetime) do
		local deltaTime = RunService.Heartbeat:Wait()
		local newLifetime = lifetime.currentLifetime + deltaTime
		
		if newLifetime >= lifetime.maxLifetime or lifetime.shouldDestroy then
			-- 生命週期結束，準備銷毀實體
			
			-- 檢查是否是寵物
			local petComponent = world:get(entityId, Components.Pet)
			if petComponent then
				print(string.format("⏰ Pet %s lifetime expired, recalling...", petComponent.petId))
				
				-- 寵物生命週期結束，回到寵物圖鑑
				world:insert(entityId, petComponent:patch({
					isActive = false,
				}))
				
				-- 移除渲染組件
				local render = world:get(entityId, Components.Render)
				if render and render.model then
					render.model.Parent = nil
				end
			end
			
			-- 檢查是否是特效
			local effectComponent = world:get(entityId, Components.Effect)
			if effectComponent then
				-- 特效結束
				local render = world:get(entityId, Components.Render)
				if render and render.model then
					render.model.Parent = nil
				end
			end
			
			-- 檢查是否是怪物屍體
			local monsterComponent = world:get(entityId, Components.Monster)
			if monsterComponent then
				local health = world:get(entityId, Components.Health)
				if health and health.isDead then
					print(string.format("🧹 Cleaning up monster %s corpse", monsterComponent.monsterId))
					
					-- 清理怪物屍體
					local render = world:get(entityId, Components.Render)
					if render and render.model then
						render.model.Parent = nil
					end
				end
			end
			
			-- 銷毀實體
			world:despawn(entityId)
		else
			-- 更新生命週期
			world:insert(entityId, lifetime:patch({
				currentLifetime = newLifetime,
			}))
			
			-- 檢查是否需要警告（生命週期快結束時）
			local remainingTime = lifetime.maxLifetime - newLifetime
			if remainingTime <= 10 and remainingTime > 9.9 then -- 剩餘10秒時警告一次
				local petComponent = world:get(entityId, Components.Pet)
				if petComponent then
					print(string.format("⚠️ Pet %s will be recalled in 10 seconds", petComponent.petId))
					-- 這裡可以發送客戶端通知
				end
			end
		end
	end
end

return lifetimeSystem
