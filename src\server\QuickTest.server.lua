--[[
	QuickTest - 快速測試修復的問題
]]

print("🧪 開始快速測試...")

-- 測試 PetDatabase 載入
local success1, PetDatabase = pcall(function()
	return require(game:GetService("ReplicatedStorage").Shared.Database.PetDatabase)
end)

if success1 then
	print("✅ PetDatabase 載入成功")
	
	-- 測試獲取寵物數據
	local slimeData = PetDatabase:GetPet("slime")
	if slimeData then
		print("✅ 成功獲取史萊姆數據:", slimeData.name)
	else
		print("❌ 無法獲取史萊姆數據")
	end
	
	-- 測試獲取所有寵物
	local allPets = PetDatabase:GetAllPets()
	print("✅ 總共有", #allPets, "隻寵物")
else
	print("❌ PetDatabase 載入失敗:", PetDatabase)
end

-- 測試 WeaponDatabase 載入
local success2, WeaponDatabase = pcall(function()
	return require(game:GetService("ReplicatedStorage").Shared.Database.WeaponDatabase)
end)

if success2 then
	print("✅ WeaponDatabase 載入成功")
	
	-- 測試獲取武器數據
	local swordData = WeaponDatabase:GetWeapon("woodenSword")
	if swordData then
		print("✅ 成功獲取木劍數據:", swordData.name)
	else
		print("❌ 無法獲取木劍數據")
	end
else
	print("❌ WeaponDatabase 載入失敗:", WeaponDatabase)
end

-- 測試 ECS 組件載入
local success3, Components = pcall(function()
	return require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
end)

if success3 then
	print("✅ ECS Components 載入成功")
	
	-- 測試創建組件
	local testComponent = Components.Health({
		maxHealth = 100,
		currentHealth = 100,
		isDead = false,
	})
	
	if testComponent then
		print("✅ 成功創建 Health 組件")
	else
		print("❌ 無法創建 Health 組件")
	end
else
	print("❌ ECS Components 載入失敗:", Components)
end

-- 測試寵物召喚修復
local success4, PetService = pcall(function()
	return require(game:GetService("ServerScriptService").Server.Services.PetService)
end)

if success4 then
	print("✅ PetService 載入成功")

	-- 測試寵物數據獲取
	local testPetData = PetDatabase:GetPet("slime")
	if testPetData then
		print("✅ 史萊姆數據獲取成功:", testPetData.name)
	else
		print("❌ 史萊姆數據獲取失敗")
	end
else
	print("❌ PetService 載入失敗:", PetService)
end

-- 測試怪物模型修復
print("🧪 檢查 workspace 中的怪物模型...")
local monsterCount = 0
for _, model in pairs(workspace:GetChildren()) do
	if model:IsA("Model") and model:FindFirstChild("MonsterId") and model:FindFirstChild("HumanoidRootPart") then
		monsterCount = monsterCount + 1
		print("✅ 找到正確格式的怪物:", model.Name)
	end
end
print("🎯 總共找到", monsterCount, "個正確格式的怪物")

-- 測試寵物數據結構修復
print("🧪 測試寵物數據結構...")
local slimeData = PetDatabase:GetPet("slime")
if slimeData and slimeData.appearance and slimeData.appearance.size then
	print("✅ 史萊姆 appearance 數據正確:", slimeData.appearance.size)
else
	print("❌ 史萊姆 appearance 數據缺失")
end

local rabbitData = PetDatabase:GetPet("rabbit")
if rabbitData and rabbitData.appearance and rabbitData.appearance.size then
	print("✅ 兔子 appearance 數據正確:", rabbitData.appearance.size)
else
	print("❌ 兔子 appearance 數據缺失")
end

-- 測試戰鬥服務修復
print("🧪 測試戰鬥服務...")
local success5, CombatService = pcall(function()
	return require(game:GetService("ServerScriptService").Server.Services.CombatService)
end)

if success5 then
	print("✅ CombatService 載入成功")
else
	print("❌ CombatService 載入失敗:", CombatService)
end

print("🧪 快速測試完成")

-- 清理測試腳本
wait(15)
script:Destroy()
