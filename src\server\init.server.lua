--[[
	Pet RPG - Server Initialization
	使用 Knit 框架 + Matter ECS 初始化服務端
]]

-- 載入依賴
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 創建 ECS World
local world = Matter.World.new()

-- 載入 ECS Systems
local SwordAttackSystem = require(script.Systems.SwordAttackSystem)
local PetFollowSystem = require(script.Systems.PetFollowSystem)
local PetAttackSystem = require(script.Systems.PetAttackSystem)
local HealthSystem = require(script.Systems.HealthSystem)
local MonsterAISystem = require(script.Systems.MonsterAISystem)
local LifetimeSystem = require(script.Systems.LifetimeSystem)

-- 創建 ECS Loop
local loop = Matter.Loop.new(world, {
	SwordAttackSystem,
	PetFollowSystem,
	PetAttackSystem,
	HealthSystem,
	MonsterAISystem,
	LifetimeSystem,
})

-- 創建遠程事件用於客戶端通信
local remoteEvents = Instance.new("Folder")
remoteEvents.Name = "RemoteEvents"
remoteEvents.Parent = ReplicatedStorage

local matterRemote = Instance.new("RemoteEvent")
matterRemote.Name = "MatterRemote"
matterRemote.Parent = remoteEvents

-- 設置全局變量供服務使用
_G.ECS_WORLD = world
_G.ECS_LOOP = loop
_G.MATTER_REMOTE = matterRemote

-- 載入所有服務（EntityService 需要先載入）
require(script.Services.EntityService)

local Services = script.Services
for _, serviceModule in pairs(Services:GetChildren()) do
	if serviceModule:IsA("ModuleScript") and serviceModule.Name ~= "EntityService" then
		require(serviceModule)
	end
end

-- 啟動 Knit 服務端
Knit.Start():andThen(function()
	print("🎮 Pet RPG Server Started!")

	-- 確保服務依賴關係
	local DataService = Knit.GetService("DataService")
	local PetService = Knit.GetService("PetService")
	local MonsterService = Knit.GetService("MonsterService")
	local CombatService = Knit.GetService("CombatService")

	-- 設置服務間依賴
	PetService.DataService = DataService
	MonsterService.DataService = DataService
	CombatService.DataService = DataService

	-- 啟動 ECS Loop
	loop:begin({
		default = RunService.Heartbeat,
	})

	print("⚔️ Combat system initialized!")
	print("⚙️ Matter ECS initialized and running")
end):catch(function(err)
	warn("❌ Server startup failed:", err)
end)
