--[[
	ZoneService - 區域服務
	處理戰鬥區域、安全區域的檢測和管理
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Zone = require(game:GetService("ReplicatedStorage").Packages.zoneplus)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)

local ZoneService = Knit.CreateService({
	Name = "ZoneService",
	Client = {
		-- 客戶端事件
		ZoneEntered = Knit.CreateSignal(),
		ZoneExited = Knit.CreateSignal(),
		ZoneStatusChanged = Knit.CreateSignal(),
	},
})

-- 私有變量
local zones = {} -- 所有區域
local playerZones = {} -- 玩家當前所在區域 {[player] = zoneData}

function ZoneService:KnitStart()
	print("🗺️ ZoneService started")
	
	-- 初始化區域
	self:_initializeZones()
	
	-- 監聽玩家離開
	game.Players.PlayerRemoving:Connect(function(player)
		playerZones[player] = nil
	end)
end

function ZoneService:KnitInit()
	-- 獲取其他服務
	self.DataService = Knit.GetService("DataService")
end

-- 初始化所有區域
function ZoneService:_initializeZones()
	-- 創建戰鬥區域
	self:_createCombatZone("CombatZone1", Vector3.new(0, 0, 0), 50)
	self:_createCombatZone("CombatZone2", Vector3.new(100, 0, 100), 40)
	
	-- 創建安全區域
	self:_createSafeZone("SafeZone1", Vector3.new(-50, 0, -50), 30)
	self:_createSafeZone("Spawn", Vector3.new(0, 0, -100), 25)
	
	-- 創建特殊區域
	self:_createSpecialZone("BossArena", Vector3.new(200, 0, 0), 60, "boss")
	self:_createSpecialZone("PetSanctuary", Vector3.new(-100, 0, 100), 35, "pet_healing")
end

-- 創建戰鬥區域
function ZoneService:_createCombatZone(zoneName, position, radius)
	local zonePart = self:_createZonePart(zoneName, position, radius)
	zonePart.BrickColor = BrickColor.new("Really red")
	zonePart.Transparency = 0.8
	
	local zone = Zone.new(zonePart)
	
	-- 進入戰鬥區域
	zone.playerEntered:Connect(function(player)
		self:_onPlayerEnteredZone(player, {
			name = zoneName,
			type = "combat",
			position = position,
			radius = radius,
			allowPvP = true,
			spawnMonsters = true,
		})
	end)
	
	-- 離開戰鬥區域
	zone.playerExited:Connect(function(player)
		self:_onPlayerExitedZone(player, zoneName)
	end)
	
	zones[zoneName] = {
		zone = zone,
		part = zonePart,
		type = "combat",
		data = {
			allowPvP = true,
			spawnMonsters = true,
			monsterLevel = 1,
		}
	}
	
	-- 創建 ECS 區域實體
	self:_createZoneEntity(zoneName, "combat", position, radius)
	
	print(string.format("🗺️ Created combat zone: %s", zoneName))
end

-- 創建安全區域
function ZoneService:_createSafeZone(zoneName, position, radius)
	local zonePart = self:_createZonePart(zoneName, position, radius)
	zonePart.BrickColor = BrickColor.new("Bright green")
	zonePart.Transparency = 0.8
	
	local zone = Zone.new(zonePart)
	
	-- 進入安全區域
	zone.playerEntered:Connect(function(player)
		self:_onPlayerEnteredZone(player, {
			name = zoneName,
			type = "safe",
			position = position,
			radius = radius,
			allowPvP = false,
			healingRate = 5, -- 每秒回復5點生命值
		})
	end)
	
	-- 離開安全區域
	zone.playerExited:Connect(function(player)
		self:_onPlayerExitedZone(player, zoneName)
	end)
	
	zones[zoneName] = {
		zone = zone,
		part = zonePart,
		type = "safe",
		data = {
			allowPvP = false,
			healingRate = 5,
		}
	}
	
	-- 創建 ECS 區域實體
	self:_createZoneEntity(zoneName, "safe", position, radius)
	
	print(string.format("🗺️ Created safe zone: %s", zoneName))
end

-- 創建特殊區域
function ZoneService:_createSpecialZone(zoneName, position, radius, specialType)
	local zonePart = self:_createZonePart(zoneName, position, radius)
	zonePart.BrickColor = BrickColor.new("Bright blue")
	zonePart.Transparency = 0.8
	
	local zone = Zone.new(zonePart)
	
	-- 進入特殊區域
	zone.playerEntered:Connect(function(player)
		local zoneData = {
			name = zoneName,
			type = "special",
			specialType = specialType,
			position = position,
			radius = radius,
		}
		
		if specialType == "boss" then
			zoneData.allowPvP = true
			zoneData.spawnBoss = true
			zoneData.bossLevel = 10
		elseif specialType == "pet_healing" then
			zoneData.allowPvP = false
			zoneData.petHealingRate = 10
		end
		
		self:_onPlayerEnteredZone(player, zoneData)
	end)
	
	-- 離開特殊區域
	zone.playerExited:Connect(function(player)
		self:_onPlayerExitedZone(player, zoneName)
	end)
	
	zones[zoneName] = {
		zone = zone,
		part = zonePart,
		type = "special",
		specialType = specialType,
		data = {}
	}
	
	-- 創建 ECS 區域實體
	self:_createZoneEntity(zoneName, "special", position, radius)
	
	print(string.format("🗺️ Created special zone: %s (%s)", zoneName, specialType))
end

-- 創建區域 Part
function ZoneService:_createZonePart(name, position, radius)
	local part = Instance.new("Part")
	part.Name = name
	part.Size = Vector3.new(radius * 2, 10, radius * 2)
	part.Position = position + Vector3.new(0, 5, 0)
	part.Anchored = true
	part.CanCollide = false
	part.Shape = Enum.PartType.Cylinder
	part.Parent = workspace
	
	-- 添加標籤
	local gui = Instance.new("BillboardGui")
	gui.Size = UDim2.new(0, 200, 0, 50)
	gui.StudsOffset = Vector3.new(0, radius + 10, 0)
	gui.Parent = part
	
	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(1, 0, 1, 0)
	label.BackgroundTransparency = 1
	label.Text = name
	label.TextColor3 = Color3.new(1, 1, 1)
	label.TextScaled = true
	label.Font = Enum.Font.SourceSansBold
	label.TextStrokeTransparency = 0
	label.TextStrokeColor3 = Color3.new(0, 0, 0)
	label.Parent = gui
	
	return part
end

-- 創建 ECS 區域實體
function ZoneService:_createZoneEntity(zoneName, zoneType, position, radius)
	local world = _G.ECS_WORLD
	if not world then return end
	
	world:spawn(
		Components.Zone({
			zoneType = zoneType,
			radius = radius,
			isActive = true,
			playersInZone = {},
		}),
		Components.Position({
			position = position,
		})
	)
end

-- 玩家進入區域
function ZoneService:_onPlayerEnteredZone(player, zoneData)
	-- 記錄玩家當前區域
	playerZones[player] = zoneData
	
	-- 通知客戶端
	self.Client.ZoneEntered:Fire(player, zoneData)
	
	-- 根據區域類型執行特殊邏輯
	if zoneData.type == "combat" then
		self:_handleCombatZoneEntry(player, zoneData)
	elseif zoneData.type == "safe" then
		self:_handleSafeZoneEntry(player, zoneData)
	elseif zoneData.type == "special" then
		self:_handleSpecialZoneEntry(player, zoneData)
	end
	
	print(string.format("🗺️ %s entered %s (%s)", player.Name, zoneData.name, zoneData.type))
end

-- 玩家離開區域
function ZoneService:_onPlayerExitedZone(player, zoneName)
	local previousZone = playerZones[player]
	if previousZone then
		-- 清除玩家區域記錄
		playerZones[player] = nil
		
		-- 通知客戶端
		self.Client.ZoneExited:Fire(player, previousZone)
		
		-- 根據區域類型執行清理邏輯
		if previousZone.type == "combat" then
			self:_handleCombatZoneExit(player, previousZone)
		elseif previousZone.type == "safe" then
			self:_handleSafeZoneExit(player, previousZone)
		elseif previousZone.type == "special" then
			self:_handleSpecialZoneExit(player, previousZone)
		end
		
		print(string.format("🗺️ %s exited %s", player.Name, zoneName))
	end
end

-- 處理戰鬥區域進入
function ZoneService:_handleCombatZoneEntry(player, zoneData)
	-- 啟用 PvP 模式
	if zoneData.allowPvP then
		-- 這裡可以設置玩家的 PvP 狀態
	end
	
	-- 生成怪物
	if zoneData.spawnMonsters then
		-- 這裡可以觸發怪物生成邏輯
	end
end

-- 處理安全區域進入
function ZoneService:_handleSafeZoneEntry(player, zoneData)
	-- 開始治療
	if zoneData.healingRate then
		-- 這裡可以開始治療邏輯
	end
end

-- 處理特殊區域進入
function ZoneService:_handleSpecialZoneEntry(player, zoneData)
	if zoneData.specialType == "boss" then
		-- Boss 區域邏輯
	elseif zoneData.specialType == "pet_healing" then
		-- 寵物治療區域邏輯
	end
end

-- 處理戰鬥區域離開
function ZoneService:_handleCombatZoneExit(player, zoneData)
	-- 清理戰鬥狀態
end

-- 處理安全區域離開
function ZoneService:_handleSafeZoneExit(player, zoneData)
	-- 停止治療
end

-- 處理特殊區域離開
function ZoneService:_handleSpecialZoneExit(player, zoneData)
	-- 清理特殊效果
end

-- 獲取玩家當前區域
function ZoneService:GetPlayerZone(player)
	return playerZones[player]
end

-- 檢查玩家是否在戰鬥區域
function ZoneService:IsPlayerInCombatZone(player)
	local zone = playerZones[player]
	return zone and zone.type == "combat"
end

-- 檢查玩家是否在安全區域
function ZoneService:IsPlayerInSafeZone(player)
	local zone = playerZones[player]
	return zone and zone.type == "safe"
end

-- 獲取區域內的所有玩家
function ZoneService:GetPlayersInZone(zoneName)
	local playersInZone = {}
	
	for player, zoneData in pairs(playerZones) do
		if zoneData.name == zoneName then
			table.insert(playersInZone, player)
		end
	end
	
	return playersInZone
end

return ZoneService
