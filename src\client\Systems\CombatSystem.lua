--[[
	CombatSystem (Client) - 客戶端戰鬥系統
	處理戰鬥特效、動畫和視覺反饋
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
local TweenService = game:GetService("TweenService")
local SoundService = game:GetService("SoundService")

-- 特效緩存
local effectCache = {}
local damageNumberGuis = {}

local function combatSystem(world)
	-- 處理揮劍特效
	for entityId, swordSwing, render in world:query(Components.SwordSwing, Components.Render) do
		if swordSwing.isSwinging and render.model then
			local character = render.model
			local humanoid = character:FindFirstChild("Humanoid")
			
			if humanoid then
				-- 播放攻擊動畫
				local animator = humanoid:FindFirstChild("Animator")
				if animator and not effectCache[entityId .. "_attack"] then
					-- 這裡可以載入攻擊動畫
					-- local attackAnim = animator:LoadAnimation(attackAnimation)
					-- attackAnim:Play()
					
					effectCache[entityId .. "_attack"] = true
					
					-- 清理標記
					game:GetService("Debris"):AddItem({
						Destroy = function()
							effectCache[entityId .. "_attack"] = nil
						end
					}, swordSwing.swingDuration)
				end
				
				-- 創建揮劍特效
				local weapon = character:FindFirstChild("Weapon") or character:FindFirstChild("Tool")
				if weapon then
					-- 創建揮劍軌跡特效
					local attachment0 = weapon:FindFirstChild("Attachment0")
					local attachment1 = weapon:FindFirstChild("Attachment1")
					
					if attachment0 and attachment1 then
						local trail = Instance.new("Trail")
						trail.Attachment0 = attachment0
						trail.Attachment1 = attachment1
						trail.Lifetime = 0.5
						trail.Color = ColorSequence.new(Color3.new(1, 1, 0))
						trail.Transparency = NumberSequence.new({
							NumberSequenceKeypoint.new(0, 0),
							NumberSequenceKeypoint.new(1, 1)
						})
						trail.Parent = weapon
						
						-- 自動清理軌跡
						game:GetService("Debris"):AddItem(trail, swordSwing.swingDuration)
					end
				end
			end
		end
	end
	
	-- 處理傷害數字顯示
	for damageId, damage, position in world:query(Components.Damage, Components.Position) do
		if not damageNumberGuis[damageId] then
			-- 創建傷害數字 GUI
			local damageGui = Instance.new("BillboardGui")
			damageGui.Size = UDim2.new(2, 0, 1, 0)
			damageGui.StudsOffset = Vector3.new(0, 3, 0)
			damageGui.Parent = workspace
			
			local damageLabel = Instance.new("TextLabel")
			damageLabel.Size = UDim2.new(1, 0, 1, 0)
			damageLabel.BackgroundTransparency = 1
			damageLabel.Text = "-" .. damage.amount
			damageLabel.TextScaled = true
			damageLabel.Font = Enum.Font.SourceSansBold
			damageLabel.TextStrokeTransparency = 0
			damageLabel.TextStrokeColor3 = Color3.new(0, 0, 0)
			
			-- 根據傷害類型設置顏色
			if damage.damageType == "physical" then
				damageLabel.TextColor3 = Color3.new(1, 0, 0) -- 紅色
			elseif damage.damageType == "magic" then
				damageLabel.TextColor3 = Color3.new(0, 0, 1) -- 藍色
			elseif damage.damageType == "pet" then
				damageLabel.TextColor3 = Color3.new(0, 1, 0) -- 綠色
			elseif damage.damageType == "monster" then
				damageLabel.TextColor3 = Color3.new(1, 0.5, 0) -- 橙色
			else
				damageLabel.TextColor3 = Color3.new(1, 1, 1) -- 白色
			end
			
			damageLabel.Parent = damageGui
			
			-- 設置位置
			damageGui.Adornee = workspace
			damageGui.CFrame = CFrame.new(position.position)
			
			-- 創建上升和淡出動畫
			local tweenInfo = TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
			local tween = TweenService:Create(damageGui, tweenInfo, {
				StudsOffset = Vector3.new(0, 6, 0)
			})
			
			local fadeTween = TweenService:Create(damageLabel, tweenInfo, {
				TextTransparency = 1,
				TextStrokeTransparency = 1
			})
			
			tween:Play()
			fadeTween:Play()
			
			-- 清理
			fadeTween.Completed:Connect(function()
				damageGui:Destroy()
				damageNumberGuis[damageId] = nil
			end)
			
			damageNumberGuis[damageId] = damageGui
		end
	end
	
	-- 處理特效組件
	for effectId, effect, position in world:query(Components.Effect, Components.Position) do
		if effect.isPlaying and not effectCache[effectId] then
			if effect.effectType == "particle" then
				-- 創建粒子特效
				local part = Instance.new("Part")
				part.Size = Vector3.new(1, 1, 1)
				part.Position = position.position
				part.Anchored = true
				part.CanCollide = false
				part.Transparency = 1
				part.Parent = workspace
				
				local attachment = Instance.new("Attachment")
				attachment.Parent = part
				
				local particles = Instance.new("ParticleEmitter")
				particles.Texture = "rbxasset://textures/particles/sparkles_main.dds"
				particles.Lifetime = NumberRange.new(0.5, 1.0)
				particles.Rate = 50
				particles.SpreadAngle = Vector2.new(45, 45)
				particles.Speed = NumberRange.new(5, 10)
				particles.Parent = attachment
				
				-- 自動清理
				game:GetService("Debris"):AddItem(part, effect.duration)
				
			elseif effect.effectType == "sound" then
				-- 播放音效
				local sound = Instance.new("Sound")
				sound.SoundId = "rbxasset://sounds/impact_water.mp3" -- 示例音效
				sound.Volume = 0.5
				sound.Parent = workspace
				sound:Play()
				
				sound.Ended:Connect(function()
					sound:Destroy()
				end)
			end
			
			effectCache[effectId] = true
		end
	end
	
	-- 清理已銷毀實體的緩存
	for key, _ in pairs(effectCache) do
		local entityId = tonumber(string.match(key, "^(%d+)"))
		if entityId and not world:contains(entityId) then
			effectCache[key] = nil
		end
	end
	
	for damageId, gui in pairs(damageNumberGuis) do
		if not world:contains(damageId) then
			if gui and gui.Parent then
				gui:Destroy()
			end
			damageNumberGuis[damageId] = nil
		end
	end
end

return combatSystem
