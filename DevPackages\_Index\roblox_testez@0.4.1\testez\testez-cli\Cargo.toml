[package]
name = "testez-cli"
description = "CLI for TestEZ, a test framework for Roblox"
version = "0.3.0"
authors = ["<PERSON> Greathouse <<EMAIL>>"]
edition = "2018"
homepage = "https://github.com/Roblox/testez"
readme = "README.md"

build = "build.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[[bin]]
name = "testez"
path = "src/main.rs"

[dependencies]
bincode = "1.2.1"
env_logger = "0.7.1"
log = "0.4.8"
maplit = "1.0.2"
rbx_dom_weak = "1.10.1"
rbx_xml = "0.11.4"
roblox_install = "0.3.0"
serde = { version = "1.0.104", features = ["derive"] }
snafu = "0.6.2"
structopt = { version = "0.3.8", default-features = false }
tempfile = "3.1.0"

[build-dependencies]
bincode = "1.2.1"
serde = { version = "1.0.104", features = ["derive"] }
