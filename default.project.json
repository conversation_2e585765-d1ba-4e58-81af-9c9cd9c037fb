{"name": "PetRPG", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "Shared": {"$path": "src/shared"}, "Packages": {"$path": "Packages"}}, "ServerScriptService": {"$className": "ServerScriptService", "Server": {"$path": "src/server"}, "Tests": {"$path": "src/tests"}}, "ServerStorage": {"$className": "ServerStorage", "ServerPackages": {"$path": "ServerPackages"}, "DevPackages": {"$path": "DevPackages"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "Client": {"$path": "src/client"}}}}}