[package]
name = "matter-ecs/matter"
description = "A modern ECS library for Roblox"
version = "0.8.5"
license = "MIT"
authors = [
    "<PERSON><PERSON>",
    "<PERSON> <<EMAIL>>",
    "Ukendio <<EMAIL>>",
]
registry = "https://github.com/UpliftGames/wally-index"
realm = "shared"
exclude = [
    "example",
    "docs",
    "Packages",
    ".moonwave",
    "build",
    "pages",
    "tests",
    "AttackOfTheKillerRoombas.rbxl",
    "AttackOfTheKillerRoombas.rbxl.lock",
    "matter.rbxl",
    "matter.rbxm",
    "roblox.toml",
]

[dev-dependencies]
TestEZ = "roblox/testez@0.4.1"
