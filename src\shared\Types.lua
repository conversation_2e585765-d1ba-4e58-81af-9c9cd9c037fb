--[[
	Types - 共享類型定義
	定義遊戲中使用的數據類型
]]

export type PlayerData = {
	-- 玩家基本信息
	level: number,
	experience: number,
	coins: number,
	
	-- 寵物相關
	pets: {Pet},
	activePet: string?,
	
	-- 遊戲進度
	unlockedAreas: {string},
	completedQuests: {string},
	
	-- 設置
	settings: PlayerSettings,
}

export type Pet = {
	id: string,
	name: string,
	species: string,
	element: string,
	rarity: string,
	level: number,
	experience: number,
	stats: PetStats,
	abilities: {string},
	isShiny: boolean?,
	isActive: boolean?,
	obtainedAt: number?,
}

export type PetStats = {
	health: number,
	attack: number,
	defense: number,
	speed: number,
}

export type PlayerSettings = {
	musicEnabled: boolean,
	soundEnabled: boolean,
}

export type QuestData = {
	id: string,
	title: string,
	description: string,
	objectives: {QuestObjective},
	rewards: {QuestReward},
	isCompleted: boolean,
}

export type QuestObjective = {
	type: string, -- "collect", "defeat", "visit" 等
	target: string,
	current: number,
	required: number,
}

export type QuestReward = {
	type: string, -- "coins", "experience", "pet" 等
	amount: number,
	itemId: string?,
}

export type PetAppearance = {
	primaryColor: Color3,
	secondaryColor: Color3,
	size: Vector3,
	glowEffect: boolean?,
}

export type PetFollowSettings = {
	distance: number,
	speed: number,
	jumpPower: number,
	canFly: boolean?,
}

export type PetConfig = {
	id: string,
	name: string,
	species: string,
	element: string,
	rarity: string,
	description: string,
	baseStats: PetStats,
	appearance: PetAppearance,
	abilities: {string},
	followSettings: PetFollowSettings,
}

return {}
