--[[
	Pet RPG - Client Initialization
	使用 Knit 框架 + Matter ECS 初始化客戶端
]]

-- 載入依賴
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 等待遠程事件
local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local matterRemote = remoteEvents:WaitForChild("MatterRemote")

-- 創建客戶端 ECS World
local world = Matter.World.new()

-- 載入客戶端 ECS Systems
local UISystem = require(script.Systems.UISystem)
local CombatSystem = require(script.Systems.CombatSystem)

-- 創建客戶端 ECS Loop
local loop = Matter.Loop.new(world, {
	UISystem,
	CombatSystem,
})

-- 設置全局變量
_G.CLIENT_ECS_WORLD = world
_G.CLIENT_ECS_LOOP = loop
_G.MATTER_REMOTE = matterRemote

-- 載入所有控制器
local Controllers = script.Controllers
for _, controllerModule in pairs(Controllers:GetChildren()) do
	if controllerModule:IsA("ModuleScript") then
		require(controllerModule)
	end
end

-- 啟動 Knit 客戶端
Knit.Start():andThen(function()
	print("🎮 Pet RPG Client Started!")

	-- 啟動客戶端 ECS Loop
	loop:begin({
		default = RunService.Heartbeat,
	})

	-- 監聽服務器 ECS 更新
	matterRemote.OnClientEvent:Connect(function(replicationData)
		-- 處理服務器發送的 ECS 狀態更新
		-- 這裡可以同步服務器的實體狀態到客戶端
	end)

	print("🎨 UI system initialized!")
	print("⚙️ Client Matter ECS initialized and running")
end):catch(function(err)
	warn("❌ Client startup failed:", err)
end)
