--[[
	PetDatabase - 寵物資料庫
	包含所有寵物的基礎數據和抽卡機率
]]

local PetDatabase = {}

-- 寵物稀有度定義
PetDatabase.Rarities = {
	common = {
		name = "普通",
		color = Color3.new(0.7, 0.7, 0.7),
		probability = 0.6, -- 60%
		multiplier = 1.0,
	},
	rare = {
		name = "稀有",
		color = Color3.new(0.3, 0.7, 1),
		probability = 0.25, -- 25%
		multiplier = 1.2,
	},
	epic = {
		name = "史詩",
		color = Color3.new(0.7, 0.3, 1),
		probability = 0.12, -- 12%
		multiplier = 1.5,
	},
	legendary = {
		name = "傳說",
		color = Color3.new(1, 0.8, 0.2),
		probability = 0.03, -- 3%
		multiplier = 2.0,
	},
}

-- 寵物基礎數據
PetDatabase.Pets = {
	-- 普通寵物
	slime = {
		id = "slime",
		name = "史萊姆",
		rarity = "common",
		baseHealth = 80,
		baseAttack = 12,
		baseSpeed = 14,
		description = "軟軟的史萊姆，很適合新手飼養",
		modelId = "rbxassetid://123456789", -- 替換為實際模型 ID
		skills = {"彈跳攻擊"},
		element = "無",
		appearance = {
			size = Vector3.new(2, 2, 2),
			primaryColor = Color3.fromRGB(100, 255, 100),
			secondaryColor = Color3.fromRGB(50, 200, 50),
		},
	},
	
	rabbit = {
		id = "rabbit",
		name = "兔子",
		rarity = "common",
		baseHealth = 70,
		baseAttack = 15,
		baseSpeed = 20,
		description = "敏捷的小兔子，移動速度很快",
		modelId = "rbxassetid://123456790",
		skills = {"快速移動"},
		element = "無",
		appearance = {
			size = Vector3.new(1.5, 2, 1.5),
			primaryColor = Color3.fromRGB(255, 255, 255),
			secondaryColor = Color3.fromRGB(255, 200, 200),
		},
	},
	
	-- 稀有寵物
	fireWolf = {
		id = "fireWolf",
		name = "火焰狼",
		rarity = "rare",
		baseHealth = 120,
		baseAttack = 25,
		baseSpeed = 18,
		description = "擁有火焰力量的狼，攻擊帶有灼燒效果",
		modelId = "rbxassetid://123456791",
		skills = {"火焰咬擊", "灼燒"},
		element = "火",
		appearance = {
			size = Vector3.new(2.5, 2.5, 3),
			primaryColor = Color3.fromRGB(255, 100, 50),
			secondaryColor = Color3.fromRGB(255, 200, 100),
		},
	},
	
	iceSpirit = {
		id = "iceSpirit",
		name = "冰霜精靈",
		rarity = "rare",
		baseHealth = 100,
		baseAttack = 22,
		baseSpeed = 16,
		description = "冰雪的化身，能夠凍結敵人",
		modelId = "rbxassetid://123456792",
		skills = {"冰霜箭", "冰凍"},
		element = "冰",
	},
	
	-- 史詩寵物
	thunderDragon = {
		id = "thunderDragon",
		name = "雷電龍",
		rarity = "epic",
		baseHealth = 180,
		baseAttack = 40,
		baseSpeed = 22,
		description = "掌控雷電的幼龍，攻擊具有麻痺效果",
		modelId = "rbxassetid://123456793",
		skills = {"雷電吐息", "閃電衝擊", "麻痺"},
		element = "雷",
	},
	
	shadowCat = {
		id = "shadowCat",
		name = "暗影貓",
		rarity = "epic",
		baseHealth = 150,
		baseAttack = 35,
		baseSpeed = 28,
		description = "來自暗影界的神秘貓咪，擅長暗殺",
		modelId = "rbxassetid://123456794",
		skills = {"暗影突襲", "隱身", "致命一擊"},
		element = "暗",
	},
	
	-- 傳說寵物
	phoenixKing = {
		id = "phoenixKing",
		name = "鳳凰王",
		rarity = "legendary",
		baseHealth = 250,
		baseAttack = 60,
		baseSpeed = 25,
		description = "不死鳥之王，擁有重生能力",
		modelId = "rbxassetid://123456795",
		skills = {"鳳凰火焰", "重生", "治癒光環", "烈焰風暴"},
		element = "聖火",
	},
	
	voidLord = {
		id = "voidLord",
		name = "虛空領主",
		rarity = "legendary",
		baseHealth = 300,
		baseAttack = 55,
		baseSpeed = 20,
		description = "來自虛空的強大存在，能夠操控空間",
		modelId = "rbxassetid://123456796",
		skills = {"虛空撕裂", "空間扭曲", "暗黑領域", "次元斬擊"},
		element = "虛空",
	},
}

-- 抽卡池配置
PetDatabase.GachaPools = {
	standard = {
		name = "標準抽卡池",
		cost = 100, -- 金幣
		pets = {
			-- 普通寵物
			{id = "slime", weight = 30},
			{id = "rabbit", weight = 30},
			
			-- 稀有寵物
			{id = "fireWolf", weight = 12},
			{id = "iceSpirit", weight = 13},
			
			-- 史詩寵物
			{id = "thunderDragon", weight = 6},
			{id = "shadowCat", weight = 6},
			
			-- 傳說寵物
			{id = "phoenixKing", weight = 2},
			{id = "voidLord", weight = 1},
		}
	},
	
	premium = {
		name = "高級抽卡池",
		cost = 250, -- 金幣
		pets = {
			-- 稀有寵物機率提升
			{id = "fireWolf", weight = 20},
			{id = "iceSpirit", weight = 20},
			
			-- 史詩寵物
			{id = "thunderDragon", weight = 15},
			{id = "shadowCat", weight = 15},
			
			-- 傳說寵物機率提升
			{id = "phoenixKing", weight = 8},
			{id = "voidLord", weight = 7},
			
			-- 普通寵物機率降低
			{id = "slime", weight = 8},
			{id = "rabbit", weight = 7},
		}
	},
}

-- 獲取寵物數據
function PetDatabase:GetPet(petId)
	return self.Pets[petId]
end

-- 獲取稀有度數據
function PetDatabase:GetRarity(rarity)
	return self.Rarities[rarity]
end

-- 獲取抽卡池
function PetDatabase:GetGachaPool(poolName)
	return self.GachaPools[poolName]
end

-- 計算寵物屬性（基於等級）
function PetDatabase:CalculateStats(petId, level)
	local petData = self:GetPet(petId)
	if not petData then
		return nil
	end
	
	local rarity = self:GetRarity(petData.rarity)
	local multiplier = rarity.multiplier
	
	-- 等級成長公式
	local levelMultiplier = 1 + (level - 1) * 0.1 -- 每級增加10%
	
	return {
		health = math.floor(petData.baseHealth * multiplier * levelMultiplier),
		attack = math.floor(petData.baseAttack * multiplier * levelMultiplier),
		speed = math.floor(petData.baseSpeed * multiplier * levelMultiplier),
	}
end

-- 獲取所有寵物列表（用於圖鑑）
function PetDatabase:GetAllPets()
	local petList = {}
	for petId, petData in pairs(self.Pets) do
		table.insert(petList, {
			id = petId,
			name = petData.name,
			rarity = petData.rarity,
			element = petData.element,
		})
	end
	
	-- 按稀有度排序
	table.sort(petList, function(a, b)
		local rarityOrder = {common = 1, rare = 2, epic = 3, legendary = 4}
		return rarityOrder[a.rarity] < rarityOrder[b.rarity]
	end)
	
	return petList
end

-- 檢查寵物是否可以進化
function PetDatabase:CanEvolve(petId, level)
	-- 簡單的進化邏輯：某些寵物在特定等級可以進化
	local evolutionData = {
		slime = {minLevel = 10, evolveTo = "kingSlime"},
		rabbit = {minLevel = 15, evolveTo = "speedRabbit"},
	}
	
	local evolution = evolutionData[petId]
	return evolution and level >= evolution.minLevel, evolution and evolution.evolveTo
end

return PetDatabase
