--[[
	GachaService - 抽卡服務
	處理寵物抽卡邏輯、機率計算和獎勵發放
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Database.PetDatabase)

local GachaService = Knit.CreateService({
	Name = "GachaService",
	Client = {
		-- 客戶端可調用的方法
		DrawPet = Knit.CreateSignal(),
		DrawMultiple = Knit.CreateSignal(),
		GetGachaPools = Knit.CreateSignal(),
		
		-- 客戶端事件
		PetDrawn = Knit.CreateSignal(),
		MultipleDrawn = Knit.CreateSignal(),
		InsufficientFunds = Knit.CreateSignal(),
	},
})

function GachaService:KnitStart()
	print("🎰 GachaService started")
	
	-- 監聽客戶端請求
	self.Client.DrawPet:Connect(function(player, poolName)
		self:_drawSinglePet(player, poolName)
	end)
	
	self.Client.DrawMultiple:Connect(function(player, poolName, count)
		self:_drawMultiplePets(player, poolName, count or 10)
	end)
	
	self.Client.GetGachaPools:Connect(function(player)
		self:_sendGachaPools(player)
	end)
end

function GachaService:KnitInit()
	-- 獲取其他服務
	self.DataService = Knit.GetService("DataService")
end

-- 單抽寵物
function GachaService:_drawSinglePet(player, poolName)
	local pool = PetDatabase:GetGachaPool(poolName)
	if not pool then
		warn("❌ Invalid gacha pool:", poolName)
		return
	end
	
	-- 檢查玩家金幣
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or playerData.coins < pool.cost then
		self.Client.InsufficientFunds:Fire(player, pool.cost, playerData and playerData.coins or 0)
		return
	end
	
	-- 扣除金幣
	self.DataService:UpdatePlayerData(player, "coins", playerData.coins - pool.cost)
	
	-- 抽取寵物
	local drawnPet = self:_rollPet(pool)
	
	-- 添加到玩家寵物收藏
	local currentPets = playerData.pets or {}
	if not currentPets[drawnPet.id] then
		currentPets[drawnPet.id] = {
			level = 1,
			experience = 0,
			rarity = drawnPet.rarity,
			obtainedAt = os.time(),
		}
	else
		-- 如果已擁有，轉換為經驗值或其他獎勵
		currentPets[drawnPet.id].experience = currentPets[drawnPet.id].experience + 50
	end
	
	self.DataService:UpdatePlayerData(player, "pets", currentPets)
	
	-- 記錄抽卡歷史
	self:_recordGachaHistory(player, drawnPet, poolName)
	
	-- 通知客戶端
	self.Client.PetDrawn:Fire(player, drawnPet, pool.cost)
	
	print(string.format("🎰 %s drew %s (%s) from %s pool", 
		player.Name, drawnPet.name, drawnPet.rarity, poolName))
end

-- 十連抽
function GachaService:_drawMultiplePets(player, poolName, count)
	local pool = PetDatabase:GetGachaPool(poolName)
	if not pool then
		warn("❌ Invalid gacha pool:", poolName)
		return
	end
	
	local totalCost = pool.cost * count
	
	-- 檢查玩家金幣
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or playerData.coins < totalCost then
		self.Client.InsufficientFunds:Fire(player, totalCost, playerData and playerData.coins or 0)
		return
	end
	
	-- 扣除金幣
	self.DataService:UpdatePlayerData(player, "coins", playerData.coins - totalCost)
	
	-- 抽取多隻寵物
	local drawnPets = {}
	local guaranteedRare = count >= 10 -- 十連保底稀有
	local hasRareOrAbove = false
	
	for i = 1, count do
		local drawnPet
		
		-- 最後一抽保底機制
		if guaranteedRare and i == count and not hasRareOrAbove then
			drawnPet = self:_rollPet(pool, "rare") -- 保底稀有
		else
			drawnPet = self:_rollPet(pool)
		end
		
		-- 檢查是否抽到稀有以上
		if drawnPet.rarity ~= "common" then
			hasRareOrAbove = true
		end
		
		table.insert(drawnPets, drawnPet)
		
		-- 添加到玩家寵物收藏
		local currentPets = playerData.pets or {}
		if not currentPets[drawnPet.id] then
			currentPets[drawnPet.id] = {
				level = 1,
				experience = 0,
				rarity = drawnPet.rarity,
				obtainedAt = os.time(),
			}
		else
			-- 重複寵物轉換為經驗值
			currentPets[drawnPet.id].experience = currentPets[drawnPet.id].experience + 50
		end
		
		playerData.pets = currentPets
	end
	
	self.DataService:UpdatePlayerData(player, "pets", playerData.pets)
	
	-- 記錄抽卡歷史
	for _, pet in ipairs(drawnPets) do
		self:_recordGachaHistory(player, pet, poolName)
	end
	
	-- 通知客戶端
	self.Client.MultipleDrawn:Fire(player, drawnPets, totalCost)
	
	print(string.format("🎰 %s drew %d pets from %s pool", 
		player.Name, count, poolName))
end

-- 抽取寵物邏輯
function GachaService:_rollPet(pool, guaranteedRarity)
	local totalWeight = 0
	local availablePets = {}
	
	-- 如果有保底稀有度，過濾寵物
	for _, petEntry in ipairs(pool.pets) do
		local petData = PetDatabase:GetPet(petEntry.id)
		if petData then
			if not guaranteedRarity or self:_isRarityOrAbove(petData.rarity, guaranteedRarity) then
				table.insert(availablePets, {
					id = petEntry.id,
					weight = petEntry.weight,
					data = petData,
				})
				totalWeight = totalWeight + petEntry.weight
			end
		end
	end
	
	-- 隨機選擇
	local randomValue = math.random() * totalWeight
	local currentWeight = 0
	
	for _, pet in ipairs(availablePets) do
		currentWeight = currentWeight + pet.weight
		if randomValue <= currentWeight then
			return pet.data
		end
	end
	
	-- 備用方案：返回第一個寵物
	return availablePets[1] and availablePets[1].data or PetDatabase:GetPet("slime")
end

-- 檢查稀有度等級
function GachaService:_isRarityOrAbove(rarity, minRarity)
	local rarityOrder = {common = 1, rare = 2, epic = 3, legendary = 4}
	return rarityOrder[rarity] >= rarityOrder[minRarity]
end

-- 記錄抽卡歷史
function GachaService:_recordGachaHistory(player, pet, poolName)
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData then return end
	
	local history = playerData.gachaHistory or {}
	
	-- 保留最近100次記錄
	if #history >= 100 then
		table.remove(history, 1)
	end
	
	table.insert(history, {
		petId = pet.id,
		petName = pet.name,
		rarity = pet.rarity,
		pool = poolName,
		timestamp = os.time(),
	})
	
	self.DataService:UpdatePlayerData(player, "gachaHistory", history)
end

-- 發送抽卡池信息給客戶端
function GachaService:_sendGachaPools(player)
	local pools = {}
	
	for poolName, poolData in pairs(PetDatabase.GachaPools) do
		pools[poolName] = {
			name = poolData.name,
			cost = poolData.cost,
			pets = {}
		}
		
		-- 添加寵物信息（不包含權重）
		for _, petEntry in ipairs(poolData.pets) do
			local petData = PetDatabase:GetPet(petEntry.id)
			if petData then
				table.insert(pools[poolName].pets, {
					id = petData.id,
					name = petData.name,
					rarity = petData.rarity,
				})
			end
		end
	end
	
	-- 發送給客戶端
	self.Client.GetGachaPools:Fire(player, pools)
end

-- 獲取玩家抽卡統計
function GachaService:GetPlayerGachaStats(player)
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or not playerData.gachaHistory then
		return {
			totalDraws = 0,
			rarityCount = {common = 0, rare = 0, epic = 0, legendary = 0},
			last10Draws = {},
		}
	end
	
	local history = playerData.gachaHistory
	local stats = {
		totalDraws = #history,
		rarityCount = {common = 0, rare = 0, epic = 0, legendary = 0},
		last10Draws = {},
	}
	
	-- 統計稀有度分布
	for _, record in ipairs(history) do
		stats.rarityCount[record.rarity] = stats.rarityCount[record.rarity] + 1
	end
	
	-- 獲取最近10次抽卡
	local startIndex = math.max(1, #history - 9)
	for i = startIndex, #history do
		table.insert(stats.last10Draws, history[i])
	end
	
	return stats
end

return GachaService
