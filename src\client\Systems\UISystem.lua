--[[
	UISystem - UI 系統
	處理客戶端 UI 更新和顯示
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)
local Players = game:GetService("Players")

local New = Fusion.New
local Value = Fusion.Value
local Computed = Fusion.Computed

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- UI 狀態
local healthBarStates = {}
local petUIStates = {}

local function uiSystem(world)
	-- 更新健康條 UI
	for entityId, health, render, playerComponent in world:query(Components.Health, Components.Render, Components.Player) do
		if playerComponent.userId == player.UserId then
			-- 更新玩家健康條
			if not healthBarStates[entityId] then
				-- 創建健康條 UI
				healthBarStates[entityId] = {
					healthState = Value(health.currentHealth),
					maxHealthState = Value(health.maxHealth),
				}
				
				local healthBar = New "ScreenGui" {
					Name = "HealthBar",
					Parent = playerGui,
					
					[Fusion.Children] = {
						New "Frame" {
							Name = "HealthFrame",
							Size = UDim2.new(0, 300, 0, 30),
							Position = UDim2.new(0, 20, 0, 20),
							BackgroundColor3 = Color3.new(0, 0, 0),
							BackgroundTransparency = 0.3,
							
							[Fusion.Children] = {
								New "Frame" {
									Name = "HealthFill",
									Size = Computed(function()
										local current = healthBarStates[entityId].healthState:get()
										local max = healthBarStates[entityId].maxHealthState:get()
										local ratio = current / max
										return UDim2.new(ratio, 0, 1, 0)
									end),
									Position = UDim2.new(0, 0, 0, 0),
									BackgroundColor3 = Color3.new(1, 0, 0),
									BorderSizePixel = 0,
								},
								
								New "TextLabel" {
									Name = "HealthText",
									Size = UDim2.new(1, 0, 1, 0),
									Position = UDim2.new(0, 0, 0, 0),
									BackgroundTransparency = 1,
									Text = Computed(function()
										local current = healthBarStates[entityId].healthState:get()
										local max = healthBarStates[entityId].maxHealthState:get()
										return string.format("%d / %d", current, max)
									end),
									TextColor3 = Color3.new(1, 1, 1),
									TextScaled = true,
									Font = Enum.Font.SourceSansBold,
								}
							}
						}
					}
				}
			else
				-- 更新健康條狀態
				healthBarStates[entityId].healthState:set(health.currentHealth)
				healthBarStates[entityId].maxHealthState:set(health.maxHealth)
			end
		end
	end
	
	-- 更新寵物 UI
	for petId, petComponent, health, position in world:query(Components.Pet, Components.Health, Components.Position) do
		if petComponent.ownerId == player.UserId and petComponent.isActive then
			if not petUIStates[petId] then
				-- 創建寵物狀態 UI
				petUIStates[petId] = {
					nameState = Value(petComponent.petId),
					levelState = Value(petComponent.level),
					healthState = Value(health.currentHealth),
					maxHealthState = Value(health.maxHealth),
				}
				
				local petUI = New "ScreenGui" {
					Name = "PetUI_" .. petId,
					Parent = playerGui,
					
					[Fusion.Children] = {
						New "Frame" {
							Name = "PetFrame",
							Size = UDim2.new(0, 200, 0, 80),
							Position = UDim2.new(1, -220, 0, 20),
							BackgroundColor3 = Color3.new(0.1, 0.1, 0.1),
							BackgroundTransparency = 0.2,
							BorderSizePixel = 2,
							BorderColor3 = Color3.new(0.5, 0.5, 0.5),
							
							[Fusion.Children] = {
								New "TextLabel" {
									Name = "PetName",
									Size = UDim2.new(1, 0, 0.4, 0),
									Position = UDim2.new(0, 0, 0, 0),
									BackgroundTransparency = 1,
									Text = Computed(function()
										local name = petUIStates[petId].nameState:get()
										local level = petUIStates[petId].levelState:get()
										return string.format("%s (Lv.%d)", name, level)
									end),
									TextColor3 = Color3.new(1, 1, 1),
									TextScaled = true,
									Font = Enum.Font.SourceSansBold,
								},
								
								New "Frame" {
									Name = "PetHealthBar",
									Size = UDim2.new(0.9, 0, 0.3, 0),
									Position = UDim2.new(0.05, 0, 0.5, 0),
									BackgroundColor3 = Color3.new(0, 0, 0),
									BackgroundTransparency = 0.3,
									
									[Fusion.Children] = {
										New "Frame" {
											Name = "PetHealthFill",
											Size = Computed(function()
												local current = petUIStates[petId].healthState:get()
												local max = petUIStates[petId].maxHealthState:get()
												local ratio = current / max
												return UDim2.new(ratio, 0, 1, 0)
											end),
											Position = UDim2.new(0, 0, 0, 0),
											BackgroundColor3 = Color3.new(0, 1, 0),
											BorderSizePixel = 0,
										}
									}
								},
								
								New "TextLabel" {
									Name = "PetHealthText",
									Size = UDim2.new(0.9, 0, 0.2, 0),
									Position = UDim2.new(0.05, 0, 0.8, 0),
									BackgroundTransparency = 1,
									Text = Computed(function()
										local current = petUIStates[petId].healthState:get()
										local max = petUIStates[petId].maxHealthState:get()
										return string.format("HP: %d/%d", current, max)
									end),
									TextColor3 = Color3.new(1, 1, 1),
									TextScaled = true,
									Font = Enum.Font.SourceSans,
								}
							}
						}
					}
				}
			else
				-- 更新寵物 UI 狀態
				petUIStates[petId].levelState:set(petComponent.level)
				petUIStates[petId].healthState:set(health.currentHealth)
				petUIStates[petId].maxHealthState:set(health.maxHealth)
			end
		elseif petUIStates[petId] and not petComponent.isActive then
			-- 寵物被收回，清理 UI
			local petUIGui = playerGui:FindFirstChild("PetUI_" .. petId)
			if petUIGui then
				petUIGui:Destroy()
			end
			petUIStates[petId] = nil
		end
	end
	
	-- 清理已銷毀實體的 UI
	for entityId, _ in pairs(healthBarStates) do
		if not world:contains(entityId) then
			healthBarStates[entityId] = nil
		end
	end
	
	for petId, _ in pairs(petUIStates) do
		if not world:contains(petId) then
			local petUIGui = playerGui:FindFirstChild("PetUI_" .. petId)
			if petUIGui then
				petUIGui:Destroy()
			end
			petUIStates[petId] = nil
		end
	end
end

return uiSystem
