--[[
	WeaponDatabase - 武器資料庫
	包含所有武器的數據和特效配置
]]

local WeaponDatabase = {}

-- 武器稀有度
WeaponDatabase.Rarities = {
	common = {
		name = "普通",
		color = Color3.new(0.7, 0.7, 0.7),
		multiplier = 1.0,
	},
	rare = {
		name = "稀有",
		color = Color3.new(0.3, 0.7, 1),
		multiplier = 1.3,
	},
	epic = {
		name = "史詩",
		color = Color3.new(0.7, 0.3, 1),
		multiplier = 1.6,
	},
	legendary = {
		name = "傳說",
		color = Color3.new(1, 0.8, 0.2),
		multiplier = 2.0,
	},
}

-- 武器數據
WeaponDatabase.Weapons = {
	-- 普通武器
	woodenSword = {
		id = "woodenSword",
		name = "木劍",
		rarity = "common",
		baseDamage = 15,
		attackSpeed = 1.0,
		range = 8,
		description = "最基礎的木製劍，適合新手使用",
		modelId = "rbxassetid://123456800",
		specialEffect = nil,
		durability = 100,
	},
	
	ironSword = {
		id = "ironSword",
		name = "鐵劍",
		rarity = "common",
		baseDamage = 22,
		attackSpeed = 0.9,
		range = 9,
		description = "堅固的鐵製劍，比木劍更加耐用",
		modelId = "rbxassetid://123456801",
		specialEffect = nil,
		durability = 150,
	},
	
	-- 稀有武器
	flameBlade = {
		id = "flameBlade",
		name = "烈焰之刃",
		rarity = "rare",
		baseDamage = 35,
		attackSpeed = 1.1,
		range = 10,
		description = "附有火焰魔法的劍，攻擊時會產生火焰效果",
		modelId = "rbxassetid://123456802",
		specialEffect = {
			type = "fire",
			damage = 5,
			duration = 3,
			particle = "fire_trail",
		},
		durability = 200,
	},
	
	frostSaber = {
		id = "frostSaber",
		name = "霜凍軍刀",
		rarity = "rare",
		baseDamage = 32,
		attackSpeed = 1.2,
		range = 9,
		description = "冰冷的軍刀，攻擊有機率凍結敵人",
		modelId = "rbxassetid://123456803",
		specialEffect = {
			type = "freeze",
			chance = 0.3,
			duration = 2,
			particle = "ice_crystals",
		},
		durability = 180,
	},
	
	-- 史詩武器
	thunderStrike = {
		id = "thunderStrike",
		name = "雷鳴重擊",
		rarity = "epic",
		baseDamage = 50,
		attackSpeed = 0.8,
		range = 12,
		description = "蘊含雷電力量的重劍，攻擊時會產生閃電",
		modelId = "rbxassetid://123456804",
		specialEffect = {
			type = "lightning",
			damage = 15,
			areaRange = 5,
			particle = "lightning_bolt",
			sound = "thunder_crash",
		},
		durability = 300,
	},
	
	shadowReaper = {
		id = "shadowReaper",
		name = "暗影收割者",
		rarity = "epic",
		baseDamage = 45,
		attackSpeed = 1.3,
		range = 11,
		description = "來自暗影界的鐮刀，攻擊會吸取敵人生命力",
		modelId = "rbxassetid://123456805",
		specialEffect = {
			type = "lifesteal",
			healPercent = 0.2,
			particle = "dark_energy",
			sound = "soul_drain",
		},
		durability = 250,
	},
	
	-- 傳說武器
	excalibur = {
		id = "excalibur",
		name = "王者之劍",
		rarity = "legendary",
		baseDamage = 80,
		attackSpeed = 1.0,
		range = 15,
		description = "傳說中的聖劍，擁有無與倫比的力量",
		modelId = "rbxassetid://123456806",
		specialEffect = {
			type = "holy",
			damage = 25,
			healSelf = 10,
			areaRange = 8,
			particle = "holy_light",
			sound = "divine_strike",
		},
		durability = 500,
	},
	
	voidCutter = {
		id = "voidCutter",
		name = "虛空切割者",
		rarity = "legendary",
		baseDamage = 75,
		attackSpeed = 1.4,
		range = 13,
		description = "能夠切割空間的神秘武器，無視防禦",
		modelId = "rbxassetid://123456807",
		specialEffect = {
			type = "void",
			trueDamage = 20,
			ignoreDefense = true,
			particle = "void_rift",
			sound = "reality_tear",
		},
		durability = 400,
	},
}

-- 武器升級材料
WeaponDatabase.UpgradeMaterials = {
	common = {
		{item = "iron_ore", amount = 5},
		{item = "wood", amount = 10},
	},
	rare = {
		{item = "magic_crystal", amount = 3},
		{item = "rare_metal", amount = 8},
	},
	epic = {
		{item = "dragon_scale", amount = 2},
		{item = "enchanted_gem", amount = 5},
	},
	legendary = {
		{item = "divine_essence", amount = 1},
		{item = "legendary_core", amount = 3},
	},
}

-- 獲取武器數據
function WeaponDatabase:GetWeapon(weaponId)
	return self.Weapons[weaponId]
end

-- 獲取稀有度數據
function WeaponDatabase:GetRarity(rarity)
	return self.Rarities[rarity]
end

-- 計算武器屬性（基於強化等級）
function WeaponDatabase:CalculateStats(weaponId, enhanceLevel)
	local weaponData = self:GetWeapon(weaponId)
	if not weaponData then
		return nil
	end
	
	local rarity = self:GetRarity(weaponData.rarity)
	local multiplier = rarity.multiplier
	
	-- 強化等級成長公式
	local enhanceMultiplier = 1 + (enhanceLevel * 0.15) -- 每級增加15%
	
	return {
		damage = math.floor(weaponData.baseDamage * multiplier * enhanceMultiplier),
		attackSpeed = weaponData.attackSpeed,
		range = weaponData.range,
		durability = math.floor(weaponData.durability * enhanceMultiplier),
	}
end

-- 獲取所有武器列表
function WeaponDatabase:GetAllWeapons()
	local weaponList = {}
	for weaponId, weaponData in pairs(self.Weapons) do
		table.insert(weaponList, {
			id = weaponId,
			name = weaponData.name,
			rarity = weaponData.rarity,
			baseDamage = weaponData.baseDamage,
		})
	end
	
	-- 按稀有度和傷害排序
	table.sort(weaponList, function(a, b)
		local rarityOrder = {common = 1, rare = 2, epic = 3, legendary = 4}
		if rarityOrder[a.rarity] == rarityOrder[b.rarity] then
			return a.baseDamage < b.baseDamage
		end
		return rarityOrder[a.rarity] < rarityOrder[b.rarity]
	end)
	
	return weaponList
end

-- 獲取升級材料需求
function WeaponDatabase:GetUpgradeCost(weaponId, currentLevel)
	local weaponData = self:GetWeapon(weaponId)
	if not weaponData then
		return nil
	end
	
	local materials = self.UpgradeMaterials[weaponData.rarity]
	local cost = {}
	
	for _, material in ipairs(materials) do
		table.insert(cost, {
			item = material.item,
			amount = material.amount * (currentLevel + 1), -- 每級需求遞增
		})
	end
	
	return cost
end

-- 檢查武器特效觸發
function WeaponDatabase:CheckSpecialEffect(weaponId)
	local weaponData = self:GetWeapon(weaponId)
	if not weaponData or not weaponData.specialEffect then
		return false, nil
	end
	
	local effect = weaponData.specialEffect
	
	-- 檢查觸發機率
	if effect.chance then
		return math.random() <= effect.chance, effect
	end
	
	-- 沒有機率限制的特效總是觸發
	return true, effect
end

return WeaponDatabase
