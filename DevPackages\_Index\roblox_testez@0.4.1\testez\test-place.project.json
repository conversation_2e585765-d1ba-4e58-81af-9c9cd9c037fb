{"name": "TestEZ Test Place", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "TestEZ": {"$path": "src"}, "TestEZTests": {"$path": "tests"}}, "ServerScriptService": {"$className": "ServerScriptService", "Run Tests": {"$path": "test/runner.server.lua"}}, "HttpService": {"$className": "HttpService", "$properties": {"HttpEnabled": true}}, "Players": {"$className": "Players", "$properties": {"CharacterAutoLoads": false}}}}