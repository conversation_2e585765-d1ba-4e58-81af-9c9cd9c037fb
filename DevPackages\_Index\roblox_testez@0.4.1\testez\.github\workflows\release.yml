name: Release

on:
  push:
    tags: ["*"]

jobs:
  windows:
    runs-on: windows-latest

    steps:
    - uses: actions/checkout@v1
      with:
        submodules: true

    - uses: rojo-rbx/setup-foreman@v1
      with:
        version: "^0.6.0"
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Build release binary
      run: cargo build --verbose --locked --release

    - name: Upload artifacts
      uses: actions/upload-artifact@v1
      with:
        name: testez-cli-win64
        path: target/release/testez.exe

  macos:
    runs-on: macos-latest

    steps:
    - uses: actions/checkout@v1
      with:
        submodules: true

    - name: Install Rust
      run: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y

    - uses: rojo-rbx/setup-foreman@v1
      with:
        version: "^0.6.0"
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Build release binary
      run: |
        source $HOME/.cargo/env
        cargo build --verbose --locked --release

    - name: Upload artifacts
      uses: actions/upload-artifact@v1
      with:
        name: testez-cli-macos
        path: target/release/testez

  linux:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v1
      with:
        submodules: true

    - uses: rojo-rbx/setup-foreman@v1
      with:
        version: "^0.6.0"
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Build
      run: cargo build --locked --verbose --release

    - name: Upload artifacts
      uses: actions/upload-artifact@v1
      with:
        name: testez-cli-linux
        path: target/release/testez