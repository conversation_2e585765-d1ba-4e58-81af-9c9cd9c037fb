--[[
	GameUIController - 遊戲 UI 控制器
	管理主要的遊戲 UI 界面
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local GameUIController = Knit.CreateController({
	Name = "GameUIController",
})

local player = Players.LocalPlayer

function GameUIController:KnitStart()
	print("🎮 GameUIController started")
	
	-- 等待 PlayerGui
	local playerGui = player:WaitForChild("PlayerGui")
	
	-- 創建主 UI
	self:_createMainUI(playerGui)
	
	-- 設置輸入處理
	self:_setupInputHandlers()
	
	-- 監聽服務事件
	self:_setupServiceListeners()
end

function GameUIController:KnitInit()
	-- 獲取服務
	self.PetService = Knit.GetService("PetService")
	self.WeaponService = Knit.GetService("WeaponService")
	self.GachaService = Knit.GetService("GachaService")
	self.CombatService = Knit.GetService("CombatService")
end

-- 創建主 UI
function GameUIController:_createMainUI(playerGui)
	-- 主 UI 容器
	local mainGui = Instance.new("ScreenGui")
	mainGui.Name = "PetRPGUI"
	mainGui.ResetOnSpawn = false
	mainGui.Parent = playerGui
	
	-- 頂部狀態欄
	self:_createStatusBar(mainGui)
	
	-- 寵物面板
	self:_createPetPanel(mainGui)
	
	-- 武器面板
	self:_createWeaponPanel(mainGui)
	
	-- 抽卡面板
	self:_createGachaPanel(mainGui)
	
	-- ECS 狀態面板
	self:_createECSStatusPanel(mainGui)
	
	self.mainGui = mainGui
	print("✅ Main UI created")
end

-- 創建狀態欄
function GameUIController:_createStatusBar(parent)
	local statusBar = Instance.new("Frame")
	statusBar.Name = "StatusBar"
	statusBar.Size = UDim2.new(1, 0, 0, 60)
	statusBar.Position = UDim2.new(0, 0, 0, 0)
	statusBar.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	statusBar.BorderSizePixel = 0
	statusBar.Parent = parent
	
	-- 玩家信息
	local playerInfo = Instance.new("TextLabel")
	playerInfo.Name = "PlayerInfo"
	playerInfo.Size = UDim2.new(0.3, 0, 1, 0)
	playerInfo.Position = UDim2.new(0, 10, 0, 0)
	playerInfo.BackgroundTransparency = 1
	playerInfo.Text = "玩家: " .. player.Name
	playerInfo.TextColor3 = Color3.new(1, 1, 1)
	playerInfo.TextScaled = true
	playerInfo.Font = Enum.Font.SourceSansBold
	playerInfo.TextXAlignment = Enum.TextXAlignment.Left
	playerInfo.Parent = statusBar
	
	-- 金幣顯示
	local coinsLabel = Instance.new("TextLabel")
	coinsLabel.Name = "CoinsLabel"
	coinsLabel.Size = UDim2.new(0.2, 0, 1, 0)
	coinsLabel.Position = UDim2.new(0.3, 0, 0, 0)
	coinsLabel.BackgroundTransparency = 1
	coinsLabel.Text = "金幣: 0"
	coinsLabel.TextColor3 = Color3.new(1, 0.8, 0.2)
	coinsLabel.TextScaled = true
	coinsLabel.Font = Enum.Font.SourceSansBold
	coinsLabel.TextXAlignment = Enum.TextXAlignment.Center
	coinsLabel.Parent = statusBar
	
	-- ECS 狀態
	local ecsStatus = Instance.new("TextLabel")
	ecsStatus.Name = "ECSStatus"
	ecsStatus.Size = UDim2.new(0.3, 0, 1, 0)
	ecsStatus.Position = UDim2.new(0.7, 0, 0, 0)
	ecsStatus.BackgroundTransparency = 1
	ecsStatus.Text = "ECS: 初始化中..."
	ecsStatus.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	ecsStatus.TextScaled = true
	ecsStatus.Font = Enum.Font.SourceSans
	ecsStatus.TextXAlignment = Enum.TextXAlignment.Right
	ecsStatus.Parent = statusBar
	
	self.statusBar = statusBar
	self.coinsLabel = coinsLabel
	self.ecsStatus = ecsStatus
end

-- 創建寵物面板
function GameUIController:_createPetPanel(parent)
	local petPanel = Instance.new("Frame")
	petPanel.Name = "PetPanel"
	petPanel.Size = UDim2.new(0, 200, 0, 150)
	petPanel.Position = UDim2.new(0, 10, 0, 70)
	petPanel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	petPanel.BorderSizePixel = 1
	petPanel.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
	petPanel.Parent = parent
	
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0, 25)
	title.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
	title.Text = "寵物系統"
	title.TextColor3 = Color3.new(1, 1, 1)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.Parent = petPanel
	
	local summonBtn = Instance.new("TextButton")
	summonBtn.Size = UDim2.new(0.45, 0, 0, 30)
	summonBtn.Position = UDim2.new(0.025, 0, 0, 35)
	summonBtn.BackgroundColor3 = Color3.new(0.2, 0.6, 0.2)
	summonBtn.Text = "召喚史萊姆"
	summonBtn.TextColor3 = Color3.new(1, 1, 1)
	summonBtn.TextScaled = true
	summonBtn.Font = Enum.Font.SourceSans
	summonBtn.Parent = petPanel
	
	local recallBtn = Instance.new("TextButton")
	recallBtn.Size = UDim2.new(0.45, 0, 0, 30)
	recallBtn.Position = UDim2.new(0.525, 0, 0, 35)
	recallBtn.BackgroundColor3 = Color3.new(0.6, 0.2, 0.2)
	recallBtn.Text = "收回寵物"
	recallBtn.TextColor3 = Color3.new(1, 1, 1)
	recallBtn.TextScaled = true
	recallBtn.Font = Enum.Font.SourceSans
	recallBtn.Parent = petPanel
	
	local statusLabel = Instance.new("TextLabel")
	statusLabel.Size = UDim2.new(1, -10, 0, 80)
	statusLabel.Position = UDim2.new(0, 5, 0, 70)
	statusLabel.BackgroundTransparency = 1
	statusLabel.Text = "狀態: 無寵物"
	statusLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	statusLabel.TextScaled = true
	statusLabel.Font = Enum.Font.SourceSans
	statusLabel.TextXAlignment = Enum.TextXAlignment.Left
	statusLabel.TextYAlignment = Enum.TextYAlignment.Top
	statusLabel.Parent = petPanel
	
	-- 按鈕事件
	summonBtn.MouseButton1Click:Connect(function()
		self.PetService.SummonPet:Fire("slime")
	end)
	
	recallBtn.MouseButton1Click:Connect(function()
		self.PetService.RecallPet:Fire()
	end)
	
	self.petPanel = petPanel
	self.petStatusLabel = statusLabel
end

-- 創建武器面板
function GameUIController:_createWeaponPanel(parent)
	local weaponPanel = Instance.new("Frame")
	weaponPanel.Name = "WeaponPanel"
	weaponPanel.Size = UDim2.new(0, 200, 0, 120)
	weaponPanel.Position = UDim2.new(0, 220, 0, 70)
	weaponPanel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	weaponPanel.BorderSizePixel = 1
	weaponPanel.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
	weaponPanel.Parent = parent
	
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0, 25)
	title.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
	title.Text = "武器系統"
	title.TextColor3 = Color3.new(1, 1, 1)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.Parent = weaponPanel
	
	local equipBtn = Instance.new("TextButton")
	equipBtn.Size = UDim2.new(0.45, 0, 0, 30)
	equipBtn.Position = UDim2.new(0.025, 0, 0, 35)
	equipBtn.BackgroundColor3 = Color3.new(0.6, 0.4, 0.2)
	equipBtn.Text = "裝備木劍"
	equipBtn.TextColor3 = Color3.new(1, 1, 1)
	equipBtn.TextScaled = true
	equipBtn.Font = Enum.Font.SourceSans
	equipBtn.Parent = weaponPanel
	
	local unequipBtn = Instance.new("TextButton")
	unequipBtn.Size = UDim2.new(0.45, 0, 0, 30)
	unequipBtn.Position = UDim2.new(0.525, 0, 0, 35)
	unequipBtn.BackgroundColor3 = Color3.new(0.4, 0.4, 0.4)
	unequipBtn.Text = "卸下武器"
	unequipBtn.TextColor3 = Color3.new(1, 1, 1)
	unequipBtn.TextScaled = true
	unequipBtn.Font = Enum.Font.SourceSans
	unequipBtn.Parent = weaponPanel
	
	local weaponStatus = Instance.new("TextLabel")
	weaponStatus.Size = UDim2.new(1, -10, 0, 50)
	weaponStatus.Position = UDim2.new(0, 5, 0, 70)
	weaponStatus.BackgroundTransparency = 1
	weaponStatus.Text = "狀態: 無武器"
	weaponStatus.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	weaponStatus.TextScaled = true
	weaponStatus.Font = Enum.Font.SourceSans
	weaponStatus.TextXAlignment = Enum.TextXAlignment.Left
	weaponStatus.TextYAlignment = Enum.TextYAlignment.Top
	weaponStatus.Parent = weaponPanel
	
	-- 按鈕事件
	equipBtn.MouseButton1Click:Connect(function()
		self.WeaponService.EquipWeapon:Fire("woodenSword")
	end)
	
	unequipBtn.MouseButton1Click:Connect(function()
		self.WeaponService.UnequipWeapon:Fire()
	end)
	
	self.weaponPanel = weaponPanel
	self.weaponStatus = weaponStatus
end

-- 創建抽卡面板
function GameUIController:_createGachaPanel(parent)
	local gachaPanel = Instance.new("Frame")
	gachaPanel.Name = "GachaPanel"
	gachaPanel.Size = UDim2.new(0, 200, 0, 100)
	gachaPanel.Position = UDim2.new(0, 430, 0, 70)
	gachaPanel.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	gachaPanel.BorderSizePixel = 1
	gachaPanel.BorderColor3 = Color3.new(0.5, 0.5, 0.5)
	gachaPanel.Parent = parent
	
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0, 25)
	title.BackgroundColor3 = Color3.new(0.3, 0.3, 0.3)
	title.Text = "抽卡系統"
	title.TextColor3 = Color3.new(1, 1, 1)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.Parent = gachaPanel
	
	local singleBtn = Instance.new("TextButton")
	singleBtn.Size = UDim2.new(0.45, 0, 0, 30)
	singleBtn.Position = UDim2.new(0.025, 0, 0, 35)
	singleBtn.BackgroundColor3 = Color3.new(0.8, 0.6, 0.2)
	singleBtn.Text = "單抽 (100)"
	singleBtn.TextColor3 = Color3.new(1, 1, 1)
	singleBtn.TextScaled = true
	singleBtn.Font = Enum.Font.SourceSans
	singleBtn.Parent = gachaPanel
	
	local multiBtn = Instance.new("TextButton")
	multiBtn.Size = UDim2.new(0.45, 0, 0, 30)
	multiBtn.Position = UDim2.new(0.525, 0, 0, 35)
	multiBtn.BackgroundColor3 = Color3.new(0.8, 0.4, 0.8)
	multiBtn.Text = "十連 (1000)"
	multiBtn.TextColor3 = Color3.new(1, 1, 1)
	multiBtn.TextScaled = true
	multiBtn.Font = Enum.Font.SourceSans
	multiBtn.Parent = gachaPanel
	
	-- 按鈕事件
	singleBtn.MouseButton1Click:Connect(function()
		self.GachaService.DrawPet:Fire("standard")
	end)
	
	multiBtn.MouseButton1Click:Connect(function()
		self.GachaService.DrawMultiple:Fire("standard", 10)
	end)
	
	self.gachaPanel = gachaPanel
end

-- 創建 ECS 狀態面板
function GameUIController:_createECSStatusPanel(parent)
	local ecsPanel = Instance.new("Frame")
	ecsPanel.Name = "ECSPanel"
	ecsPanel.Size = UDim2.new(0, 300, 0, 150)
	ecsPanel.Position = UDim2.new(1, -310, 0, 70)
	ecsPanel.BackgroundColor3 = Color3.new(0.1, 0.1, 0.2)
	ecsPanel.BorderSizePixel = 1
	ecsPanel.BorderColor3 = Color3.new(0.3, 0.3, 0.6)
	ecsPanel.Parent = parent
	
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0, 25)
	title.BackgroundColor3 = Color3.new(0.2, 0.2, 0.4)
	title.Text = "ECS 系統狀態"
	title.TextColor3 = Color3.new(1, 1, 1)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.Parent = ecsPanel
	
	local statusText = Instance.new("TextLabel")
	statusText.Size = UDim2.new(1, -10, 1, -30)
	statusText.Position = UDim2.new(0, 5, 0, 30)
	statusText.BackgroundTransparency = 1
	statusText.Text = "正在檢查 ECS 狀態..."
	statusText.TextColor3 = Color3.new(0.8, 0.8, 1)
	statusText.TextScaled = true
	statusText.Font = Enum.Font.SourceSans
	statusText.TextXAlignment = Enum.TextXAlignment.Left
	statusText.TextYAlignment = Enum.TextYAlignment.Top
	statusText.Parent = ecsPanel
	
	self.ecsPanel = ecsPanel
	self.ecsStatusText = statusText
	
	-- 定期更新 ECS 狀態
	spawn(function()
		while self.ecsStatusText do
			wait(2)
			self:_updateECSStatus()
		end
	end)
end

-- 更新 ECS 狀態
function GameUIController:_updateECSStatus()
	if not self.ecsStatusText then return end
	
	local world = _G.CLIENT_ECS_WORLD
	local loop = _G.CLIENT_ECS_LOOP
	local remote = _G.MATTER_REMOTE
	
	local status = "客戶端 ECS 狀態:\n"
	status = status .. "World: " .. (world and "✅ 運行中" or "❌ 未找到") .. "\n"
	status = status .. "Loop: " .. (loop and "✅ 運行中" or "❌ 未找到") .. "\n"
	status = status .. "Remote: " .. (remote and "✅ 已連接" or "❌ 未連接") .. "\n"
	status = status .. "時間: " .. os.date("%H:%M:%S")
	
	self.ecsStatusText.Text = status
	
	-- 更新頂部狀態
	if self.ecsStatus then
		local ecsStatusShort = (world and loop) and "ECS: ✅ 運行中" or "ECS: ❌ 錯誤"
		self.ecsStatus.Text = ecsStatusShort
		self.ecsStatus.TextColor3 = (world and loop) and Color3.new(0.2, 0.8, 0.2) or Color3.new(0.8, 0.2, 0.2)
	end
end

-- 設置輸入處理
function GameUIController:_setupInputHandlers()
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		if input.KeyCode == Enum.KeyCode.Tab then
			-- 切換 UI 顯示
			if self.mainGui then
				self.mainGui.Enabled = not self.mainGui.Enabled
			end
		end
	end)
end

-- 設置服務監聽
function GameUIController:_setupServiceListeners()
	-- 監聽寵物事件
	if self.PetService then
		self.PetService.PetSummoned:Connect(function(petId)
			if self.petStatusLabel then
				self.petStatusLabel.Text = "狀態: 已召喚 " .. petId
			end
		end)

		self.PetService.PetRecalled:Connect(function()
			if self.petStatusLabel then
				self.petStatusLabel.Text = "狀態: 無寵物"
			end
		end)
	end

	-- 監聽武器事件
	if self.WeaponService then
		self.WeaponService.WeaponEquipped:Connect(function(weaponId, stats)
			if self.weaponStatus then
				self.weaponStatus.Text = string.format("已裝備: %s\n傷害: %d", weaponId, stats.damage)
			end
		end)

		self.WeaponService.WeaponUnequipped:Connect(function()
			if self.weaponStatus then
				self.weaponStatus.Text = "狀態: 無武器"
			end
		end)
	end

	-- 監聽抽卡事件
	if self.GachaService then
		self.GachaService.PetDrawn:Connect(function(petData, cost)
			print("🎰 抽到寵物:", petData.name, "稀有度:", petData.rarity)
			-- 可以在這裡顯示抽卡結果 UI
			self:_showGachaResult(petData, false)
		end)

		self.GachaService.MultipleDrawn:Connect(function(petsData, totalCost)
			print("🎰 十連抽結果:", #petsData, "隻寵物")
			-- 可以在這裡顯示十連抽結果 UI
			self:_showGachaResult(petsData, true)
		end)
	end
end

-- 顯示抽卡結果
function GameUIController:_showGachaResult(data, isMultiple)
	local message = ""
	if isMultiple then
		message = string.format("十連抽獲得 %d 隻寵物！", #data)
	else
		message = string.format("獲得 %s (%s)！", data.name, data.rarity)
	end

	-- 創建臨時提示
	local playerGui = game.Players.LocalPlayer:WaitForChild("PlayerGui")
	local notification = Instance.new("ScreenGui")
	notification.Name = "GachaNotification"
	notification.Parent = playerGui

	local frame = Instance.new("Frame")
	frame.Size = UDim2.new(0, 400, 0, 100)
	frame.Position = UDim2.new(0.5, -200, 0.3, 0)
	frame.BackgroundColor3 = Color3.new(0.8, 0.6, 0.2)
	frame.BorderSizePixel = 2
	frame.BorderColor3 = Color3.new(1, 0.8, 0.4)
	frame.Parent = notification

	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(1, 0, 1, 0)
	label.BackgroundTransparency = 1
	label.Text = message
	label.TextColor3 = Color3.new(1, 1, 1)
	label.TextScaled = true
	label.Font = Enum.Font.SourceSansBold
	label.Parent = frame

	-- 3秒後自動消失
	game:GetService("Debris"):AddItem(notification, 3)
end

return GameUIController
