{"name": "matter", "tree": {"$className": "DataModel", "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "$path": "tests"}}, "ReplicatedStorage": {"$className": "ReplicatedStorage", "$path": "DevPackages", "Matter": {"$path": "lib"}}, "TestService": {"$properties": {"ExecuteWithStudioRun": true}, "$className": "TestService", "run": {"$path": "tests.server.luau"}}}}