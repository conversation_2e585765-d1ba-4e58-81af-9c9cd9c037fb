[package]
name = "sleitnick/knit-bundle"
version = "0.1.0"
registry = "https://github.com/UpliftGames/wally-index"
realm = "shared"

[dependencies]
Comm = "sleitnick/comm@^1"
Component = "sleitnick/component@^2"
EnumList = "sleitnick/enum-list@^2"
Input = "sleitnick/input@^2"
Option = "sleitnick/option@^1"
Signal = "sleitnick/signal@^2"
Streamable = "sleitnick/streamable@^1"
TableUtil = "sleitnick/table-util@^1"
Timer = "sleitnick/timer@^1"
Trove = "sleitnick/trove@^1"
Promise = "evaera/promise@^4"
