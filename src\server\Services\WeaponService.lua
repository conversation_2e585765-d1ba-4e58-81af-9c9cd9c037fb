--[[
	WeaponService - 武器服務
	處理武器裝備、升級和特效觸發
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local WeaponDatabase = require(game:GetService("ReplicatedStorage").Shared.Database.WeaponDatabase)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)

local WeaponService = Knit.CreateService({
	Name = "WeaponService",
	Client = {
		-- 客戶端可調用的方法
		EquipWeapon = Knit.CreateSignal(),
		UnequipWeapon = Knit.CreateSignal(),
		UpgradeWeapon = Knit.CreateSignal(),
		GetWeaponList = Knit.CreateSignal(),
		
		-- 客戶端事件
		WeaponEquipped = Knit.CreateSignal(),
		WeaponUnequipped = Knit.CreateSignal(),
		WeaponUpgraded = Knit.CreateSignal(),
		UpgradeFailed = Knit.CreateSignal(),
		WeaponEffect = Knit.CreateSignal(),
	},
})

-- 私有變量
local equippedWeapons = {} -- {[player] = weaponData}

function WeaponService:KnitStart()
	print("⚔️ WeaponService started")
	
	-- 監聽客戶端請求
	self.Client.EquipWeapon:Connect(function(player, weaponId)
		self:_equipWeapon(player, weaponId)
	end)
	
	self.Client.UnequipWeapon:Connect(function(player)
		self:_unequipWeapon(player)
	end)
	
	self.Client.UpgradeWeapon:Connect(function(player, weaponId)
		self:_upgradeWeapon(player, weaponId)
	end)
	
	self.Client.GetWeaponList:Connect(function(player)
		self:_sendWeaponList(player)
	end)
	
	-- 監聽玩家離開
	game.Players.PlayerRemoving:Connect(function(player)
		equippedWeapons[player] = nil
	end)
end

function WeaponService:KnitInit()
	-- 獲取其他服務
	self.DataService = Knit.GetService("DataService")
	self.EntityService = Knit.GetService("EntityService")
end

-- 裝備武器
function WeaponService:_equipWeapon(player, weaponId)
	-- 檢查武器是否存在
	local weaponData = WeaponDatabase:GetWeapon(weaponId)
	if not weaponData then
		warn("❌ Weapon not found:", weaponId)
		return
	end
	
	-- 檢查玩家是否擁有此武器
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or not playerData.weapons or not playerData.weapons[weaponId] then
		warn("❌ Player doesn't own weapon:", weaponId)
		return
	end
	
	-- 卸下當前武器
	if equippedWeapons[player] then
		self:_unequipWeapon(player)
	end
	
	-- 獲取武器強化等級
	local enhanceLevel = playerData.weapons[weaponId].enhanceLevel or 0
	local weaponStats = WeaponDatabase:CalculateStats(weaponId, enhanceLevel)
	
	-- 創建武器模型
	local weaponModel = self:_createWeaponModel(weaponData, player)
	if weaponModel then
		-- 儲存裝備信息
		equippedWeapons[player] = {
			id = weaponId,
			data = weaponData,
			stats = weaponStats,
			enhanceLevel = enhanceLevel,
			model = weaponModel,
		}
		
		-- 更新玩家 ECS 實體
		self:_updatePlayerWeaponComponent(player, weaponData, weaponStats)
		
		-- 更新玩家數據
		self.DataService:UpdatePlayerData(player, "equippedWeapon", weaponId)
		
		-- 通知客戶端
		self.Client.WeaponEquipped:Fire(player, weaponId, weaponStats)
		
		print(string.format("⚔️ %s equipped %s", player.Name, weaponData.name))
	end
end

-- 卸下武器
function WeaponService:_unequipWeapon(player)
	local equippedWeapon = equippedWeapons[player]
	if equippedWeapon then
		-- 銷毀武器模型
		if equippedWeapon.model then
			equippedWeapon.model:Destroy()
		end
		
		-- 移除 ECS 武器組件
		self:_removePlayerWeaponComponent(player)
		
		-- 清除裝備信息
		equippedWeapons[player] = nil
		
		-- 更新玩家數據
		self.DataService:UpdatePlayerData(player, "equippedWeapon", nil)
		
		-- 通知客戶端
		self.Client.WeaponUnequipped:Fire(player)
		
		print(string.format("⚔️ %s unequipped weapon", player.Name))
	end
end

-- 升級武器
function WeaponService:_upgradeWeapon(player, weaponId)
	local weaponData = WeaponDatabase:GetWeapon(weaponId)
	if not weaponData then
		warn("❌ Weapon not found:", weaponId)
		return
	end
	
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or not playerData.weapons or not playerData.weapons[weaponId] then
		warn("❌ Player doesn't own weapon:", weaponId)
		return
	end
	
	local currentLevel = playerData.weapons[weaponId].enhanceLevel or 0
	local maxLevel = 20 -- 最大強化等級
	
	if currentLevel >= maxLevel then
		self.Client.UpgradeFailed:Fire(player, "已達到最大強化等級")
		return
	end
	
	-- 檢查升級材料
	local upgradeCost = WeaponDatabase:GetUpgradeCost(weaponId, currentLevel)
	if not self:_checkUpgradeMaterials(player, upgradeCost) then
		self.Client.UpgradeFailed:Fire(player, "材料不足")
		return
	end
	
	-- 扣除材料
	self:_consumeUpgradeMaterials(player, upgradeCost)
	
	-- 升級武器
	local newLevel = currentLevel + 1
	playerData.weapons[weaponId].enhanceLevel = newLevel
	self.DataService:UpdatePlayerData(player, "weapons", playerData.weapons)
	
	-- 如果武器正在裝備中，更新屬性
	if equippedWeapons[player] and equippedWeapons[player].id == weaponId then
		local newStats = WeaponDatabase:CalculateStats(weaponId, newLevel)
		equippedWeapons[player].stats = newStats
		equippedWeapons[player].enhanceLevel = newLevel
		
		-- 更新 ECS 組件
		self:_updatePlayerWeaponComponent(player, weaponData, newStats)
	end
	
	-- 通知客戶端
	self.Client.WeaponUpgraded:Fire(player, weaponId, newLevel)
	
	print(string.format("⚔️ %s upgraded %s to level %d", player.Name, weaponData.name, newLevel))
end

-- 創建武器模型
function WeaponService:_createWeaponModel(weaponData, player)
	local character = player.Character
	if not character then return nil end
	
	-- 這裡應該根據 weaponData.modelId 載入實際的武器模型
	-- 暫時創建一個簡單的 Part 作為示例
	local weapon = Instance.new("Tool")
	weapon.Name = weaponData.name
	weapon.RequiresHandle = true
	
	local handle = Instance.new("Part")
	handle.Name = "Handle"
	handle.Size = Vector3.new(0.2, 4, 0.2)
	handle.Material = Enum.Material.Metal
	handle.BrickColor = BrickColor.new("Really black")
	handle.Parent = weapon
	
	-- 添加特效附件點
	local attachment0 = Instance.new("Attachment")
	attachment0.Name = "Attachment0"
	attachment0.Position = Vector3.new(0, -2, 0)
	attachment0.Parent = handle
	
	local attachment1 = Instance.new("Attachment")
	attachment1.Name = "Attachment1"
	attachment1.Position = Vector3.new(0, 2, 0)
	attachment1.Parent = handle
	
	-- 裝備到玩家
	weapon.Parent = player.Backpack
	
	return weapon
end

-- 更新玩家武器組件
function WeaponService:_updatePlayerWeaponComponent(player, weaponData, weaponStats)
	local world = _G.ECS_WORLD
	if not world then return end
	
	local playerEntityId = self:_getPlayerEntityId(player)
	if playerEntityId then
		-- 添加或更新武器組件
		world:insert(playerEntityId, Components.Weapon({
			weaponId = weaponData.id,
			damage = weaponStats.damage,
			attackSpeed = weaponStats.attackSpeed,
			range = weaponStats.range,
			specialEffect = weaponData.specialEffect,
		}))
		
		-- 更新揮劍組件
		world:insert(playerEntityId, Components.SwordSwing({
			isSwinging = false,
			swingDuration = 1.0 / weaponStats.attackSpeed,
			swingStartTime = 0,
			damage = weaponStats.damage,
			range = weaponStats.range,
		}))
	end
end

-- 移除玩家武器組件
function WeaponService:_removePlayerWeaponComponent(player)
	local world = _G.ECS_WORLD
	if not world then return end
	
	local playerEntityId = self:_getPlayerEntityId(player)
	if playerEntityId then
		world:remove(playerEntityId, Components.Weapon)
		world:remove(playerEntityId, Components.SwordSwing)
	end
end

-- 獲取玩家實體 ID
function WeaponService:_getPlayerEntityId(player)
	return self.EntityService:GetPlayerEntityId(player)
end

-- 檢查升級材料
function WeaponService:_checkUpgradeMaterials(player, upgradeCost)
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or not playerData.materials then
		return false
	end
	
	for _, cost in ipairs(upgradeCost) do
		local owned = playerData.materials[cost.item] or 0
		if owned < cost.amount then
			return false
		end
	end
	
	return true
end

-- 消耗升級材料
function WeaponService:_consumeUpgradeMaterials(player, upgradeCost)
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData.materials then
		playerData.materials = {}
	end
	
	for _, cost in ipairs(upgradeCost) do
		playerData.materials[cost.item] = (playerData.materials[cost.item] or 0) - cost.amount
	end
	
	self.DataService:UpdatePlayerData(player, "materials", playerData.materials)
end

-- 發送武器列表給客戶端
function WeaponService:_sendWeaponList(player)
	local playerData = self.DataService:GetPlayerData(player)
	local weaponList = {}
	
	if playerData and playerData.weapons then
		for weaponId, weaponInfo in pairs(playerData.weapons) do
			local weaponData = WeaponDatabase:GetWeapon(weaponId)
			if weaponData then
				local stats = WeaponDatabase:CalculateStats(weaponId, weaponInfo.enhanceLevel or 0)
				table.insert(weaponList, {
					id = weaponId,
					name = weaponData.name,
					rarity = weaponData.rarity,
					enhanceLevel = weaponInfo.enhanceLevel or 0,
					stats = stats,
					isEquipped = (playerData.equippedWeapon == weaponId),
				})
			end
		end
	end
	
	self.Client.GetWeaponList:Fire(player, weaponList)
end

-- 觸發武器特效
function WeaponService:TriggerWeaponEffect(player, targetPosition)
	local equippedWeapon = equippedWeapons[player]
	if not equippedWeapon or not equippedWeapon.data.specialEffect then
		return false
	end
	
	local shouldTrigger, effect = WeaponDatabase:CheckSpecialEffect(equippedWeapon.id)
	if shouldTrigger then
		-- 通知客戶端播放特效
		self.Client.WeaponEffect:Fire(player, effect, targetPosition)
		
		print(string.format("✨ %s triggered %s weapon effect", 
			player.Name, equippedWeapon.data.name))
		
		return true, effect
	end
	
	return false
end

-- 獲取玩家裝備的武器
function WeaponService:GetEquippedWeapon(player)
	return equippedWeapons[player]
end

return WeaponService
