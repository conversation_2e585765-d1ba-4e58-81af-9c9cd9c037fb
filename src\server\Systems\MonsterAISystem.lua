--[[
	MonsterAISystem - 怪物 AI 系統
	處理怪物的巡邏、追擊和攻擊行為
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
local RunService = game:GetService("RunService")

local function monsterAISystem(world)
	-- 查詢所有怪物實體
	for monsterId, monster, ai, position, health, render in world:query(
		Components.Monster,
		Components.AI,
		Components.Position,
		Components.Health,
		Components.Render
	) do
		if not health.isDead then
			local currentTime = tick()
			local deltaTime = RunService.Heartbeat:Wait()
			
			-- 根據 AI 狀態執行不同行為
			if ai.state == "idle" then
				-- 閒置狀態：檢測附近的玩家
				local nearestPlayer = nil
				local nearestDistance = math.huge
				
				-- 查詢所有玩家
				for playerId, playerComponent, playerPosition in world:query(Components.Player, Components.Position) do
					local distance = (playerPosition.position - position.position).Magnitude
					if distance <= monster.detectionRange and distance < nearestDistance then
						nearestPlayer = playerId
						nearestDistance = distance
					end
				end
				
				if nearestPlayer then
					-- 發現玩家，切換到追擊狀態
					world:insert(monsterId, ai:patch({
						state = "chase",
						stateChangeTime = currentTime,
					}))
					
					-- 添加目標組件
					world:insert(monsterId, Components.Target({
						targetId = nearestPlayer,
						attackRange = 5,
						lastAttackTime = 0,
						attackCooldown = 2.0,
					}))
				else
					-- 沒有發現玩家，開始巡邏
					if currentTime - ai.stateChangeTime > 3.0 then -- 3秒後開始巡邏
						world:insert(monsterId, ai:patch({
							state = "patrol",
							stateChangeTime = currentTime,
							targetPosition = ai.homePosition + Vector3.new(
								math.random(-monster.patrolRadius, monster.patrolRadius),
								0,
								math.random(-monster.patrolRadius, monster.patrolRadius)
							),
						}))
					end
				end
				
			elseif ai.state == "patrol" then
				-- 巡邏狀態：移動到目標位置
				local distance = (ai.targetPosition - position.position).Magnitude
				
				if distance > 2 then
					-- 移動到目標位置
					local direction = (ai.targetPosition - position.position).Unit
					local speed = 8 -- 巡邏速度
					local newPosition = position.position + direction * speed * deltaTime
					
					world:insert(monsterId, position:patch({
						position = newPosition,
					}))
					
					-- 更新 3D 模型位置
					if render.model and render.primaryPart then
						render.primaryPart.CFrame = CFrame.new(newPosition, newPosition + direction)
					end
				else
					-- 到達目標位置，回到閒置狀態
					world:insert(monsterId, ai:patch({
						state = "idle",
						stateChangeTime = currentTime,
					}))
				end
				
				-- 在巡邏時也檢測玩家
				for playerId, playerComponent, playerPosition in world:query(Components.Player, Components.Position) do
					local distance = (playerPosition.position - position.position).Magnitude
					if distance <= monster.detectionRange then
						-- 發現玩家，切換到追擊狀態
						world:insert(monsterId, ai:patch({
							state = "chase",
							stateChangeTime = currentTime,
						}))
						
						world:insert(monsterId, Components.Target({
							targetId = playerId,
							attackRange = 5,
							lastAttackTime = 0,
							attackCooldown = 2.0,
						}))
						break
					end
				end
				
			elseif ai.state == "chase" then
				-- 追擊狀態：追擊目標
				local target = world:get(monsterId, Components.Target)
				if target and target.targetId then
					local targetPosition = world:get(target.targetId, Components.Position)
					local targetHealth = world:get(target.targetId, Components.Health)
					
					if targetPosition and targetHealth and not targetHealth.isDead then
						local distance = (targetPosition.position - position.position).Magnitude
						
						if distance <= target.attackRange then
							-- 進入攻擊範圍，切換到攻擊狀態
							world:insert(monsterId, ai:patch({
								state = "attack",
								stateChangeTime = currentTime,
							}))
						elseif distance <= monster.detectionRange * 1.5 then
							-- 繼續追擊
							local direction = (targetPosition.position - position.position).Unit
							local speed = 12 -- 追擊速度
							local newPosition = position.position + direction * speed * deltaTime
							
							world:insert(monsterId, position:patch({
								position = newPosition,
							}))
							
							-- 更新 3D 模型位置
							if render.model and render.primaryPart then
								render.primaryPart.CFrame = CFrame.new(newPosition, newPosition + direction)
							end
						else
							-- 目標太遠，返回巡邏狀態
							world:insert(monsterId, ai:patch({
								state = "return",
								stateChangeTime = currentTime,
								targetPosition = ai.homePosition,
							}))
							
							-- 移除目標組件
							world:remove(monsterId, Components.Target)
						end
					else
						-- 目標不存在或已死亡，返回閒置狀態
						world:insert(monsterId, ai:patch({
							state = "idle",
							stateChangeTime = currentTime,
						}))
						
						world:remove(monsterId, Components.Target)
					end
				end
				
			elseif ai.state == "attack" then
				-- 攻擊狀態：攻擊目標
				local target = world:get(monsterId, Components.Target)
				if target and target.targetId then
					if currentTime - target.lastAttackTime >= target.attackCooldown then
						local targetHealth = world:get(target.targetId, Components.Health)
						local targetPosition = world:get(target.targetId, Components.Position)
						
						if targetHealth and targetPosition and not targetHealth.isDead then
							local distance = (targetPosition.position - position.position).Magnitude
							
							if distance <= target.attackRange then
								-- 執行攻擊
								local damage = monster.attackDamage
								local newHealth = math.max(0, targetHealth.currentHealth - damage)
								local isDead = newHealth <= 0
								
								world:insert(target.targetId, targetHealth:patch({
									currentHealth = newHealth,
									isDead = isDead,
								}))
								
								-- 更新攻擊時間
								world:insert(monsterId, target:patch({
									lastAttackTime = currentTime,
								}))
								
								-- 創建傷害效果
								world:spawn(
									Components.Damage({
										amount = damage,
										damageType = "monster",
										source = monsterId,
									}),
									Components.Position({
										position = targetPosition.position,
									}),
									Components.Lifetime({
										maxLifetime = 1.0,
										currentLifetime = 0,
									})
								)
								
								print(string.format("👹 Monster %s attacked for %d damage", monster.monsterId, damage))
								
								if isDead then
									-- 目標死亡，返回閒置狀態
									world:insert(monsterId, ai:patch({
										state = "idle",
										stateChangeTime = currentTime,
									}))
									
									world:remove(monsterId, Components.Target)
								else
									-- 繼續攻擊
									world:insert(monsterId, ai:patch({
										state = "chase",
										stateChangeTime = currentTime,
									}))
								end
							else
								-- 目標離開攻擊範圍，回到追擊狀態
								world:insert(monsterId, ai:patch({
									state = "chase",
									stateChangeTime = currentTime,
								}))
							end
						end
					end
				end
				
			elseif ai.state == "return" then
				-- 返回狀態：回到出生點
				local distance = (ai.homePosition - position.position).Magnitude
				
				if distance > 2 then
					local direction = (ai.homePosition - position.position).Unit
					local speed = 10 -- 返回速度
					local newPosition = position.position + direction * speed * deltaTime
					
					world:insert(monsterId, position:patch({
						position = newPosition,
					}))
					
					-- 更新 3D 模型位置
					if render.model and render.primaryPart then
						render.primaryPart.CFrame = CFrame.new(newPosition, newPosition + direction)
					end
				else
					-- 回到出生點，切換到閒置狀態
					world:insert(monsterId, ai:patch({
						state = "idle",
						stateChangeTime = currentTime,
					}))
				end
			end
		end
	end
end

return monsterAISystem
