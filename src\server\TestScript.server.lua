--[[
	TestScript - 測試 Matter ECS 整合
	用於驗證系統是否正常工作
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

-- 等待 Knit 啟動
Knit.OnStart():andThen(function()
	print("🧪 Starting ECS Integration Tests...")
	
	-- 等待服務初始化
	wait(2)
	
	-- 獲取服務
	local EntityService = Knit.GetService("EntityService")
	local PetService = Knit.GetService("PetService")
	local MonsterSpawnService = Knit.GetService("MonsterSpawnService")
	local WeaponService = Knit.GetService("WeaponService")
	local GachaService = Knit.GetService("GachaService")
	
	-- 測試函數
	local function testEntityService()
		print("🧪 Testing EntityService...")
		
		-- 測試玩家實體
		local players = Players:GetPlayers()
		for _, player in pairs(players) do
			local entityId = EntityService:GetPlayerEntityId(player)
			if entityId then
				print("✅ Player entity found for", player.Name, "ID:", tostring(entityId))
			else
				print("❌ No player entity found for", player.Name)
			end
		end
	end
	
	local function testMonsterSpawning()
		print("🧪 Testing Monster Spawning...")
		
		-- 手動生成一隻測試怪物
		local testPosition = Vector3.new(0, 0, 20)
		local instanceId = MonsterSpawnService:ForceSpawnMonster("goblin", testPosition, 1)
		
		if instanceId then
			print("✅ Test monster spawned:", instanceId)
			
			-- 5秒後清理
			wait(5)
			local monsterInfo = MonsterSpawnService:GetMonsterInfo(instanceId)
			if monsterInfo then
				print("✅ Monster info retrieved:", monsterInfo.monsterId)
			end
		else
			print("❌ Failed to spawn test monster")
		end
	end
	
	local function testPetSystem()
		print("🧪 Testing Pet System...")
		
		local players = Players:GetPlayers()
		if #players > 0 then
			local testPlayer = players[1]
			
			-- 測試寵物召喚（需要先給玩家一隻寵物）
			local DataService = Knit.GetService("DataService")
			local playerData = DataService:GetPlayerData(testPlayer)
			
			if playerData then
				-- 確保玩家有寵物
				if not playerData.pets then
					playerData.pets = {}
				end
				
				if not playerData.pets.slime then
					playerData.pets.slime = {
						level = 1,
						experience = 0,
						rarity = "common",
						obtainedAt = os.time(),
					}
					DataService:UpdatePlayerData(testPlayer, "pets", playerData.pets)
					print("✅ Added test pet to player")
				end
				
				-- 測試召喚
				PetService.Client.SummonPet:Fire(testPlayer, "slime")
				print("✅ Pet summon command sent")
				
				-- 等待一下再測試收回
				wait(3)
				PetService.Client.RecallPet:Fire(testPlayer)
				print("✅ Pet recall command sent")
			end
		end
	end
	
	local function testWeaponSystem()
		print("🧪 Testing Weapon System...")
		
		local players = Players:GetPlayers()
		if #players > 0 then
			local testPlayer = players[1]
			
			-- 給玩家一把測試武器
			local DataService = Knit.GetService("DataService")
			local playerData = DataService:GetPlayerData(testPlayer)
			
			if playerData then
				if not playerData.weapons then
					playerData.weapons = {}
				end
				
				if not playerData.weapons.woodenSword then
					playerData.weapons.woodenSword = {
						enhanceLevel = 0,
						obtainedAt = os.time(),
					}
					DataService:UpdatePlayerData(testPlayer, "weapons", playerData.weapons)
					print("✅ Added test weapon to player")
				end
				
				-- 測試裝備武器
				WeaponService.Client.EquipWeapon:Fire(testPlayer, "woodenSword")
				print("✅ Weapon equip command sent")
				
				-- 等待一下再測試卸下
				wait(3)
				WeaponService.Client.UnequipWeapon:Fire(testPlayer)
				print("✅ Weapon unequip command sent")
			end
		end
	end
	
	local function testGachaSystem()
		print("🧪 Testing Gacha System...")
		
		local players = Players:GetPlayers()
		if #players > 0 then
			local testPlayer = players[1]
			
			-- 給玩家一些金幣
			local DataService = Knit.GetService("DataService")
			local playerData = DataService:GetPlayerData(testPlayer)
			
			if playerData then
				if (playerData.coins or 0) < 1000 then
					DataService:UpdatePlayerData(testPlayer, "coins", 1000)
					print("✅ Added test coins to player")
				end
				
				-- 測試單抽
				GachaService.Client.DrawPet:Fire(testPlayer, "standard")
				print("✅ Single gacha draw command sent")
				
				-- 等待一下再測試十連
				wait(2)
				GachaService.Client.DrawMultiple:Fire(testPlayer, "standard", 10)
				print("✅ Multiple gacha draw command sent")
			end
		end
	end
	
	local function testECSWorld()
		print("🧪 Testing ECS World...")
		
		local world = _G.ECS_WORLD
		if world then
			print("✅ ECS World found")
			
			-- 檢查 ECS Loop
			local loop = _G.ECS_LOOP
			if loop then
				print("✅ ECS Loop found")
			else
				print("❌ ECS Loop not found")
			end
		else
			print("❌ ECS World not found")
		end
	end
	
	-- 運行所有測試
	local function runAllTests()
		print("🧪 ========== ECS Integration Tests ==========")
		
		testECSWorld()
		wait(1)
		
		testEntityService()
		wait(1)
		
		testMonsterSpawning()
		wait(1)
		
		testPetSystem()
		wait(1)
		
		testWeaponSystem()
		wait(1)
		
		testGachaSystem()
		wait(1)
		
		print("🧪 ========== Tests Completed ==========")
	end
	
	-- 延遲執行測試，確保所有系統都已初始化
	wait(5)
	runAllTests()
	
	-- 設置定期測試（每60秒）
	spawn(function()
		while true do
			wait(60)
			print("🧪 Running periodic system check...")
			testECSWorld()
			testEntityService()
		end
	end)
	
end):catch(function(err)
	warn("❌ Test script failed to start:", err)
end)
