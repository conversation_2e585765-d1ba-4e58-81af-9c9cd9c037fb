--[[
	PetFollowSystem - 寵物跟隨系統
	處理寵物跟隨主人的邏輯
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
local RunService = game:GetService("RunService")

local function petFollowSystem(world)
	-- 查詢所有有跟隨目標組件的寵物
	for petId, followTarget, petPosition, petRender, petComponent in world:query(
		Components.FollowTarget, 
		Components.Position, 
		Components.Render, 
		Components.Pet
	) do
		if followTarget.isFollowing and followTarget.targetId then
			-- 獲取目標位置
			local targetPosition = world:get(followTarget.targetId, Components.Position)
			local targetRender = world:get(followTarget.targetId, Components.Render)
			
			if targetPosition and targetRender then
				local petPos = petPosition.position
				local targetPos = targetPosition.position
				local distance = (targetPos - petPos).Magnitude
				
				-- 如果距離超過跟隨距離，開始移動
				if distance > followTarget.followDistance then
					-- 計算移動方向
					local direction = (targetPos - petPos).Unit
					local newPosition = petPos + direction * followTarget.speed * RunService.Heartbeat:Wait()
					
					-- 更新寵物位置
					world:insert(petId, petPosition:patch({
						position = newPosition,
					}))
					
					-- 更新 3D 模型位置
					if petRender.model and petRender.primaryPart then
						petRender.primaryPart.CFrame = CFrame.new(newPosition, newPosition + direction)
					end
					
					-- 添加移動組件
					local movement = world:get(petId, Components.Movement) or Components.Movement()
					world:insert(petId, movement:patch({
						velocity = direction * followTarget.speed,
						isMoving = true,
					}))
				else
					-- 停止移動
					local movement = world:get(petId, Components.Movement)
					if movement then
						world:insert(petId, movement:patch({
							velocity = Vector3.new(0, 0, 0),
							isMoving = false,
						}))
					end
				end
			end
		end
	end
end

return petFollowSystem
