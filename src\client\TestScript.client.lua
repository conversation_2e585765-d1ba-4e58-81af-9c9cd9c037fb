--[[
	ClientTestScript - 客戶端 ECS 測試
	測試客戶端 ECS 系統和 UI
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer

-- 等待 Knit 啟動
Knit.OnStart():andThen(function()
	print("🧪 Starting Client ECS Tests...")
	
	-- 等待客戶端系統初始化
	wait(2)
	
	-- 測試函數
	local function testClientECS()
		print("🧪 Testing Client ECS...")
		
		local world = _G.CLIENT_ECS_WORLD
		if world then
			print("✅ Client ECS World found")
			
			local loop = _G.CLIENT_ECS_LOOP
			if loop then
				print("✅ Client ECS Loop found")
			else
				print("❌ Client ECS Loop not found")
			end
		else
			print("❌ Client ECS World not found")
		end
	end
	
	local function testRemoteConnection()
		print("🧪 Testing Remote Connection...")
		
		local matterRemote = _G.MATTER_REMOTE
		if matterRemote then
			print("✅ Matter Remote found")
			
			-- 測試發送消息到服務器
			matterRemote:FireServer("test_message", {
				type = "client_test",
				timestamp = tick(),
				playerId = player.UserId,
			})
			print("✅ Test message sent to server")
		else
			print("❌ Matter Remote not found")
		end
	end
	
	local function testUISystem()
		print("🧪 Testing UI System...")
		
		-- 檢查是否有 UI 相關的全局變量或狀態
		local playerGui = player:WaitForChild("PlayerGui")
		
		-- 創建測試 UI
		local testGui = Instance.new("ScreenGui")
		testGui.Name = "ECSTestGui"
		testGui.Parent = playerGui
		
		local testFrame = Instance.new("Frame")
		testFrame.Size = UDim2.new(0, 300, 0, 200)
		testFrame.Position = UDim2.new(0, 10, 0, 10)
		testFrame.BackgroundColor3 = Color3.new(0, 0, 0)
		testFrame.BackgroundTransparency = 0.3
		testFrame.BorderSizePixel = 2
		testFrame.BorderColor3 = Color3.new(1, 1, 1)
		testFrame.Parent = testGui
		
		local titleLabel = Instance.new("TextLabel")
		titleLabel.Size = UDim2.new(1, 0, 0, 30)
		titleLabel.BackgroundTransparency = 1
		titleLabel.Text = "ECS Test Panel"
		titleLabel.TextColor3 = Color3.new(1, 1, 1)
		titleLabel.TextScaled = true
		titleLabel.Font = Enum.Font.SourceSansBold
		titleLabel.Parent = testFrame
		
		local statusLabel = Instance.new("TextLabel")
		statusLabel.Size = UDim2.new(1, -20, 1, -40)
		statusLabel.Position = UDim2.new(0, 10, 0, 35)
		statusLabel.BackgroundTransparency = 1
		statusLabel.Text = "Client ECS Status: Running\nPress T to test pet summon\nPress Y to test weapon equip\nPress U to test gacha draw"
		statusLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
		statusLabel.TextScaled = true
		statusLabel.Font = Enum.Font.SourceSans
		statusLabel.TextXAlignment = Enum.TextXAlignment.Left
		statusLabel.TextYAlignment = Enum.TextYAlignment.Top
		statusLabel.Parent = testFrame
		
		print("✅ Test UI created")
		
		-- 5秒後移除測試 UI
		game:GetService("Debris"):AddItem(testGui, 10)
	end
	
	local function setupInputHandlers()
		print("🧪 Setting up input handlers...")
		
		UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed then return end
			
			if input.KeyCode == Enum.KeyCode.T then
				-- 測試寵物召喚
				print("🧪 Testing pet summon via input...")
				local PetService = Knit.GetService("PetService")
				if PetService then
					PetService.SummonPet:Fire("slime")
				end
				
			elseif input.KeyCode == Enum.KeyCode.Y then
				-- 測試武器裝備
				print("🧪 Testing weapon equip via input...")
				local WeaponService = Knit.GetService("WeaponService")
				if WeaponService then
					WeaponService.EquipWeapon:Fire("woodenSword")
				end
				
			elseif input.KeyCode == Enum.KeyCode.U then
				-- 測試抽卡
				print("🧪 Testing gacha draw via input...")
				local GachaService = Knit.GetService("GachaService")
				if GachaService then
					GachaService.DrawPet:Fire("standard")
				end
				
			elseif input.KeyCode == Enum.KeyCode.I then
				-- 顯示系統信息
				print("🧪 System Info:")
				print("Client ECS World:", _G.CLIENT_ECS_WORLD and "✅" or "❌")
				print("Client ECS Loop:", _G.CLIENT_ECS_LOOP and "✅" or "❌")
				print("Matter Remote:", _G.MATTER_REMOTE and "✅" or "❌")
				
			elseif input.KeyCode == Enum.KeyCode.O then
				-- 測試怪物生成
				print("🧪 Requesting monster spawn...")
				local matterRemote = _G.MATTER_REMOTE
				if matterRemote then
					matterRemote:FireServer("spawn_test_monster", {
						position = player.Character and player.Character.PrimaryPart.Position + Vector3.new(10, 0, 0) or Vector3.new(10, 0, 10),
						monsterId = "goblin",
						level = 1,
					})
				end
			end
		end)
		
		print("✅ Input handlers set up")
		print("📝 Controls:")
		print("  T - Test pet summon")
		print("  Y - Test weapon equip")
		print("  U - Test gacha draw")
		print("  I - Show system info")
		print("  O - Spawn test monster")
	end
	
	local function testServiceConnections()
		print("🧪 Testing service connections...")
		
		-- 測試連接到各個服務
		local services = {"PetService", "WeaponService", "GachaService", "CombatService"}
		
		for _, serviceName in ipairs(services) do
			local success, service = pcall(function()
				return Knit.GetService(serviceName)
			end)
			
			if success and service then
				print("✅", serviceName, "connected")
			else
				print("❌", serviceName, "connection failed")
			end
		end
	end
	
	local function monitorECSPerformance()
		print("🧪 Starting ECS performance monitoring...")
		
		spawn(function()
			while true do
				wait(10) -- 每10秒檢查一次
				
				local world = _G.CLIENT_ECS_WORLD
				if world then
					-- 這裡可以添加性能監控邏輯
					-- 例如實體數量、系統執行時間等
					print("🔍 Client ECS World active")
				else
					warn("⚠️ Client ECS World lost!")
				end
			end
		end)
	end
	
	-- 運行所有測試
	local function runAllTests()
		print("🧪 ========== Client ECS Tests ==========")
		
		testClientECS()
		wait(0.5)
		
		testRemoteConnection()
		wait(0.5)
		
		testUISystem()
		wait(0.5)
		
		testServiceConnections()
		wait(0.5)
		
		setupInputHandlers()
		wait(0.5)
		
		monitorECSPerformance()
		
		print("🧪 ========== Client Tests Completed ==========")
	end
	
	-- 延遲執行測試
	wait(3)
	runAllTests()
	
	-- 監聽服務器 ECS 更新
	local matterRemote = _G.MATTER_REMOTE
	if matterRemote then
		matterRemote.OnClientEvent:Connect(function(eventType, data)
			if eventType == "test_response" then
				print("📨 Received test response from server:", data)
			elseif eventType == "ecs_update" then
				-- 處理 ECS 狀態更新
				print("🔄 ECS update received")
			else
				print("📨 Unknown event from server:", eventType, data)
			end
		end)
	end
	
end):catch(function(err)
	warn("❌ Client test script failed to start:", err)
end)
