--[[
	SwordAttackSystem - 揮劍攻擊系統
	處理玩家揮劍攻擊的邏輯，包括命中判定和傷害計算
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)
local RunService = game:GetService("RunService")

local function swordAttackSystem(world)
	-- 查詢所有有揮劍組件的實體
	for entityId, swordSwing, position, render in world:query(Components.SwordSwing, Components.Position, Components.Render) do
		if swordSwing.isSwinging then
			local currentTime = tick()
			local swingProgress = (currentTime - swordSwing.swingStartTime) / swordSwing.swingDuration
			
			-- 檢查揮劍是否完成
			if swingProgress >= 1.0 then
				-- 結束揮劍
				world:insert(entityId, swordSwing:patch({
					isSwinging = false,
					swingStartTime = 0,
				}))
			else
				-- 在揮劍過程中檢測命中
				local attackerPosition = position.position
				local attackRange = swordSwing.range
				
				-- 查詢所有可能的目標（怪物和其他玩家）
				for targetId, targetHealth, targetPosition, targetRender in world:query(Components.Health, Components.Position, Components.Render) do
					if targetId ~= entityId and not targetHealth.isDead then
						local distance = (targetPosition.position - attackerPosition).Magnitude
						
						-- 檢查是否在攻擊範圍內
						if distance <= attackRange then
							-- 計算傷害
							local damage = swordSwing.damage
							
							-- 檢查目標是否有武器組件（可能影響防禦）
							local targetWeapon = world:get(targetId, Components.Weapon)
							if targetWeapon then
								-- 可以在這裡添加防禦計算邏輯
							end
							
							-- 造成傷害
							local newHealth = math.max(0, targetHealth.currentHealth - damage)
							local isDead = newHealth <= 0
							
							world:insert(targetId, targetHealth:patch({
								currentHealth = newHealth,
								isDead = isDead,
							}))
							
							-- 創建傷害組件用於視覺效果
							world:spawn(
								Components.Damage({
									amount = damage,
									damageType = "physical",
									source = entityId,
								}),
								Components.Position({
									position = targetPosition.position,
								}),
								Components.Lifetime({
									maxLifetime = 2.0,
									currentLifetime = 0,
								})
							)
							
							-- 如果目標死亡，處理死亡邏輯
							if isDead then
								-- 檢查是否是怪物
								local monsterComponent = world:get(targetId, Components.Monster)
								if monsterComponent then
									-- 怪物死亡，給予獎勵
									local playerComponent = world:get(entityId, Components.Player)
									if playerComponent then
										-- 可以在這裡添加經驗值和金幣獎勵邏輯
									end
								end
							end
							
							print(string.format("⚔️ Entity %d attacked Entity %d for %d damage", entityId, targetId, damage))
						end
					end
				end
			end
		end
	end
end

return swordAttackSystem
