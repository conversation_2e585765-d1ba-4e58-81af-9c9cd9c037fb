--[[
	DataService - 數據管理服務
	使用 ProfileService 處理玩家數據持久化
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local ProfileService = require(game:GetService("ServerStorage").ServerPackages.profileservice)
local Players = game:GetService("Players")

local DataService = Knit.CreateService({
	Name = "DataService",
	Client = {},
})

-- 數據模板
local DATA_TEMPLATE = {
	-- 玩家基本信息
	level = 1,
	experience = 0,
	coins = 100,
	
	-- 寵物相關
	pets = {
		-- 預設給玩家第一隻寵物
		["fire_fox"] = {
			id = "fire_fox",
			level = 1,
			experience = 0,
			isShiny = false,
			isActive = false,
			obtainedAt = os.time(),
		}
	},
	activePet = nil,
	petDex = {
		discovered = {"fire_fox"}, -- 已發現的寵物
		total = 0, -- 總收集數量
	},
	
	-- 遊戲進度
	unlockedAreas = {"StartingArea"},
	completedQuests = {},
	
	-- 設置
	settings = {
		musicEnabled = true,
		soundEnabled = true,
	},
}

-- 私有變量
local profileStore
local profiles = {}

function DataService:KnitStart()
	print("💾 DataService started")
	
	-- 創建 ProfileStore
	profileStore = ProfileService.GetProfileStore("PlayerData", DATA_TEMPLATE)
	
	-- 監聽玩家加入
	Players.PlayerAdded:Connect(function(player)
		self:_loadPlayerProfile(player)
	end)
	
	-- 監聽玩家離開
	Players.PlayerRemoving:Connect(function(player)
		self:_releasePlayerProfile(player)
	end)
	
	-- 處理已經在遊戲中的玩家
	for _, player in pairs(Players:GetPlayers()) do
		self:_loadPlayerProfile(player)
	end
end

function DataService:KnitInit()
	-- 初始化服務依賴
end

-- 載入玩家檔案
function DataService:_loadPlayerProfile(player)
	local profile = profileStore:LoadProfileAsync("Player_" .. player.UserId)
	
	if profile ~= nil then
		profile:AddUserId(player.UserId)
		profile:Reconcile()
		
		profile:ListenToRelease(function()
			profiles[player] = nil
			player:Kick("數據釋放 - 請重新加入遊戲")
		end)
		
		if player.Parent == Players then
			profiles[player] = profile
			print("✅ Profile loaded for:", player.Name)
		else
			profile:Release()
		end
	else
		player:Kick("數據載入失敗 - 請重新加入遊戲")
	end
end

-- 釋放玩家檔案
function DataService:_releasePlayerProfile(player)
	local profile = profiles[player]
	if profile ~= nil then
		profile:Release()
		profiles[player] = nil
		print("💾 Profile released for:", player.Name)
	end
end

-- 獲取玩家數據
function DataService:GetPlayerData(player)
	local profile = profiles[player]
	return profile and profile.Data or nil
end

-- 更新玩家數據
function DataService:UpdatePlayerData(player, key, value)
	local profile = profiles[player]
	if profile then
		profile.Data[key] = value
		return true
	end
	return false
end

-- 增加玩家金幣
function DataService:AddCoins(player, amount)
	local data = self:GetPlayerData(player)
	if data then
		data.coins = data.coins + amount
		return data.coins
	end
	return 0
end

-- 增加玩家經驗值
function DataService:AddExperience(player, amount)
	local data = self:GetPlayerData(player)
	if data then
		data.experience = data.experience + amount
		return data.experience
	end
	return nil
end

return DataService
