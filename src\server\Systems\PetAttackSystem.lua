--[[
	PetAttackSystem - 寵物攻擊系統
	處理寵物自動攻擊目標的邏輯
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)

local function petAttackSystem(world)
	-- 查詢所有有目標組件的寵物
	for petId, target, petPosition, petComponent, petHealth in world:query(
		Components.Target,
		Components.Position,
		Components.Pet,
		Components.Health
	) do
		if not petHealth.isDead and target.targetId then
			local currentTime = tick()
			
			-- 檢查攻擊冷卻
			if currentTime - target.lastAttackTime >= target.attackCooldown then
				-- 獲取目標
				local targetHealth = world:get(target.targetId, Components.Health)
				local targetPosition = world:get(target.targetId, Components.Position)
				
				if targetHealth and targetPosition and not targetHealth.isDead then
					local distance = (targetPosition.position - petPosition.position).Magnitude
					
					-- 檢查是否在攻擊範圍內
					if distance <= target.attackRange then
						-- 計算寵物攻擊傷害（基於等級和稀有度）
						local baseDamage = 15
						local levelBonus = petComponent.level * 2
						local rarityMultiplier = 1.0
						
						if petComponent.rarity == "rare" then
							rarityMultiplier = 1.2
						elseif petComponent.rarity == "epic" then
							rarityMultiplier = 1.5
						elseif petComponent.rarity == "legendary" then
							rarityMultiplier = 2.0
						end
						
						local totalDamage = math.floor((baseDamage + levelBonus) * rarityMultiplier)
						
						-- 造成傷害
						local newHealth = math.max(0, targetHealth.currentHealth - totalDamage)
						local isDead = newHealth <= 0
						
						world:insert(target.targetId, targetHealth:patch({
							currentHealth = newHealth,
							isDead = isDead,
						}))
						
						-- 更新攻擊時間
						world:insert(petId, target:patch({
							lastAttackTime = currentTime,
						}))
						
						-- 創建傷害效果
						world:spawn(
							Components.Damage({
								amount = totalDamage,
								damageType = "pet",
								source = petId,
							}),
							Components.Position({
								position = targetPosition.position,
							}),
							Components.Lifetime({
								maxLifetime = 1.5,
								currentLifetime = 0,
							})
						)
						
						-- 如果目標死亡，清除目標
						if isDead then
							world:insert(petId, target:patch({
								targetId = nil,
							}))
							
							-- 給寵物增加經驗值
							local newExp = petComponent.experience + 10
							local newLevel = petComponent.level
							
							-- 簡單的升級邏輯
							if newExp >= petComponent.level * 100 then
								newLevel = newLevel + 1
								newExp = 0
							end
							
							world:insert(petId, petComponent:patch({
								experience = newExp,
								level = newLevel,
							}))
						end
						
						print(string.format("🐾 Pet %s attacked target for %d damage", petComponent.petId, totalDamage))
					end
				else
					-- 目標不存在或已死亡，清除目標
					world:insert(petId, target:patch({
						targetId = nil,
					}))
				end
			end
		end
	end
end

return petAttackSystem
