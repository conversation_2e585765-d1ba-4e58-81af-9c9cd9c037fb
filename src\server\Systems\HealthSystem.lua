--[[
	HealthSystem - 健康系統
	處理傷害、治療和死亡邏輯
]]

local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)

local function healthSystem(world)
	-- 處理所有有健康組件的實體
	for entityId, health, render in world:query(Components.Health, Components.Render) do
		-- 檢查健康值變化
		if health.currentHealth <= 0 and not health.isDead then
			-- 實體死亡
			world:insert(entityId, health:patch({
				isDead = true,
				currentHealth = 0,
			}))
			
			-- 檢查是否是玩家
			local playerComponent = world:get(entityId, Components.Player)
			if playerComponent then
				-- 玩家死亡處理
				print(string.format("💀 Player %s died", playerComponent.playerName))
				
				-- 可以在這裡添加復活邏輯
				-- 例如：傳送到安全區域，扣除金幣等
			end
			
			-- 檢查是否是寵物
			local petComponent = world:get(entityId, Components.Pet)
			if petComponent then
				-- 寵物死亡處理
				print(string.format("💀 Pet %s died", petComponent.petId))
				
				-- 寵物死亡後可能需要回到寵物圖鑑
				world:insert(entityId, petComponent:patch({
					isActive = false,
				}))
			end
			
			-- 檢查是否是怪物
			local monsterComponent = world:get(entityId, Components.Monster)
			if monsterComponent then
				-- 怪物死亡處理
				print(string.format("💀 Monster %s died", monsterComponent.monsterId))
				
				-- 生成掉落物品
				if #monsterComponent.dropRewards > 0 then
					local position = world:get(entityId, Components.Position)
					if position then
						-- 在這裡可以創建掉落物品實體
						-- 暫時只打印掉落信息
						for _, reward in ipairs(monsterComponent.dropRewards) do
							print(string.format("💰 Dropped: %s", reward))
						end
					end
				end
				
				-- 標記怪物為待銷毀
				world:insert(entityId, Components.Lifetime({
					maxLifetime = 5.0, -- 5秒後清理屍體
					currentLifetime = 0,
					shouldDestroy = true,
				}))
			end
			
			-- 隱藏或修改 3D 模型
			if render.model then
				-- 可以播放死亡動畫或隱藏模型
				render.model.Parent = nil
			end
		end
		
		-- 健康值回復邏輯（如果需要）
		if health.currentHealth > 0 and health.currentHealth < health.maxHealth then
			-- 可以在這裡添加自動回復邏輯
			-- 例如：每秒回復 1 點生命值
		end
	end
	
	-- 處理傷害組件（用於視覺效果）
	for damageId, damage, position, lifetime in world:query(Components.Damage, Components.Position, Components.Lifetime) do
		-- 傷害數字顯示邏輯
		-- 這裡可以創建 BillboardGui 來顯示傷害數字
		
		-- 更新生命週期
		local newLifetime = lifetime.currentLifetime + game:GetService("RunService").Heartbeat:Wait()
		
		if newLifetime >= lifetime.maxLifetime then
			-- 銷毀傷害效果
			world:despawn(damageId)
		else
			world:insert(damageId, lifetime:patch({
				currentLifetime = newLifetime,
			}))
		end
	end
end

return healthSystem
