--[[
	PetService - 寵物管理服務
	處理寵物召喚、收回、數據管理等功能
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Database.PetDatabase)
local Components = require(game:GetService("ReplicatedStorage").Shared.ECS.Components)

local PetService = Knit.CreateService({
	Name = "PetService",
	Client = {
		-- 客戶端可調用的方法
		SummonPet = Knit.CreateSignal(),
		RecallPet = Knit.CreateSignal(),
		GetPetDex = Knit.CreateSignal(),
		PetSummoned = Knit.CreateSignal(),
		PetRecalled = Knit.CreateSignal(),
	},
})

-- 私有變量
local activePets = {} -- 當前召喚的寵物 {[player] = petModel}

function PetService:KnitStart()
	print("🐾 PetService started")
	
	-- 監聽客戶端請求
	self.Client.SummonPet:Connect(function(player, petId)
		self:_summonPet(player, petId)
	end)
	
	self.Client.RecallPet:Connect(function(player)
		self:_recallPet(player)
	end)
	
	self.Client.GetPetDex:Connect(function(player)
		self:_sendPetDex(player)
	end)

	-- 監聽玩家離開事件
	Players.PlayerRemoving:Connect(function(player)
		self:_cleanupPlayerPets(player)
	end)
end

function PetService:KnitInit()
	-- 獲取其他服務
	self.DataService = Knit.GetService("DataService")
	self.EntityService = Knit.GetService("EntityService")
end

-- 召喚寵物
function PetService:_summonPet(player, petId)
	-- 檢查玩家是否擁有這隻寵物
	local playerData = self.DataService:GetPlayerData(player)
	if not playerData or not playerData.pets[petId] then
		warn("Player", player.Name, "doesn't own pet:", petId)
		return
	end
	
	-- 防止重複召喚同一隻寵物
	if activePets[player] then
		print("🐾 Pet already active for", player.Name, "- recalling first")
		self:_recallPet(player)
		wait(0.1) -- 短暫等待確保清理完成
	end
	
	-- 創建寵物模型
	local petModel = self:_createPetModel(petId, player)
	if petModel then
		activePets[player] = petModel

		-- 創建 ECS 實體
		local petData = PetDatabase:GetPet(petId)
		if petData then
			local petEntityId = self.EntityService:CreatePetEntity(
				player,
				petId,
				petData,
				playerData.pets[petId].level or 1,
				playerData.pets[petId].experience or 0,
				playerData.pets[petId].rarity or "common"
			)

			-- 將實體 ID 存儲到模型中
			if petEntityId then
				petModel:SetAttribute("EntityId", tostring(petEntityId))
			end
		end

		-- 更新玩家數據
		self.DataService:UpdatePlayerData(player, "activePet", petId)

		-- 通知客戶端
		self.Client.PetSummoned:Fire(player, petId)
		print("🐾 Pet summoned:", petId, "for", player.Name)
	end
end

-- 收回寵物
function PetService:_recallPet(player)
	local petModel = activePets[player]
	if petModel then
		-- 獲取當前活躍寵物 ID
		local playerData = self.DataService:GetPlayerData(player)
		local activePetId = playerData and playerData.activePet

		-- 銷毀 ECS 實體
		if activePetId then
			self.EntityService:DestroyPetEntity(player, activePetId)
		end

		-- 銷毀模型
		petModel:Destroy()
		activePets[player] = nil

		-- 更新玩家數據
		self.DataService:UpdatePlayerData(player, "activePet", nil)

		-- 通知客戶端
		self.Client.PetRecalled:Fire(player)
		print("🐾 Pet recalled for", player.Name)
	end
end

-- 創建寵物模型
function PetService:_createPetModel(petId, owner)
	local petConfig = PetDatabase:GetPet(petId)
	if not petConfig then
		warn("Pet config not found:", petId)
		return nil
	end
	
	-- 創建寵物模型
	local petModel = Instance.new("Model")
	petModel.Name = petConfig.name
	petModel.Parent = workspace
	
	-- 創建主體部分
	local body = Instance.new("Part")
	body.Name = "Body"
	body.Size = petConfig.appearance.size
	body.Color = petConfig.appearance.primaryColor
	body.Material = Enum.Material.Neon
	body.Shape = Enum.PartType.Block
	body.TopSurface = Enum.SurfaceType.Smooth
	body.BottomSurface = Enum.SurfaceType.Smooth
	body.CanCollide = false
	body.Parent = petModel
	
	-- 添加圓角
	local corner = Instance.new("SpecialMesh")
	corner.MeshType = Enum.MeshType.Sphere
	corner.Parent = body
	
	-- 創建眼睛
	local leftEye = Instance.new("Part")
	leftEye.Name = "LeftEye"
	leftEye.Size = Vector3.new(0.3, 0.3, 0.3)
	leftEye.Color = Color3.fromRGB(0, 0, 0)
	leftEye.Material = Enum.Material.Neon
	leftEye.Shape = Enum.PartType.Ball
	leftEye.CanCollide = false
	leftEye.Parent = petModel
	
	local rightEye = leftEye:Clone()
	rightEye.Name = "RightEye"
	rightEye.Parent = petModel
	
	-- 焊接眼睛到身體
	local leftWeld = Instance.new("WeldConstraint")
	leftWeld.Part0 = body
	leftWeld.Part1 = leftEye
	leftWeld.Parent = body
	leftEye.CFrame = body.CFrame * CFrame.new(-0.4, 0.3, -petConfig.appearance.size.Z/2 + 0.1)
	
	local rightWeld = Instance.new("WeldConstraint")
	rightWeld.Part0 = body
	rightWeld.Part1 = rightEye
	rightWeld.Parent = body
	rightEye.CFrame = body.CFrame * CFrame.new(0.4, 0.3, -petConfig.appearance.size.Z/2 + 0.1)
	
	-- 添加發光效果
	if petConfig.appearance.glowEffect then
		local pointLight = Instance.new("PointLight")
		pointLight.Color = petConfig.appearance.primaryColor
		pointLight.Brightness = 0.5
		pointLight.Range = 10
		pointLight.Parent = body
	end
	
	-- 創建 Humanoid 用於移動
	local humanoid = Instance.new("Humanoid")
	humanoid.MaxHealth = 100
	humanoid.Health = 100
	humanoid.WalkSpeed = petConfig.followSettings.speed
	humanoid.JumpPower = petConfig.followSettings.jumpPower
	humanoid.Parent = petModel
	
	-- 創建 HumanoidRootPart
	local rootPart = Instance.new("Part")
	rootPart.Name = "HumanoidRootPart"
	rootPart.Size = Vector3.new(2, 2, 1) -- 增大尺寸確保穩定
	rootPart.Transparency = 1
	rootPart.CanCollide = false -- 保持 false 避免卡住
	rootPart.Anchored = false -- 確保不被錨定
	rootPart.Parent = petModel
	
	-- 焊接身體到根部件
	local bodyWeld = Instance.new("WeldConstraint")
	bodyWeld.Part0 = rootPart
	bodyWeld.Part1 = body
	bodyWeld.Parent = rootPart
	
	-- 設置主要部件
	petModel.PrimaryPart = rootPart
	
	-- 添加寵物標籤
	local ownerValue = Instance.new("ObjectValue")
	ownerValue.Name = "Owner"
	ownerValue.Value = owner
	ownerValue.Parent = petModel
	
	local petIdValue = Instance.new("StringValue")
	petIdValue.Name = "PetId"
	petIdValue.Value = petId
	petIdValue.Parent = petModel
	
	-- 設置初始位置（在玩家旁邊）
	if owner.Character and owner.Character:FindFirstChild("HumanoidRootPart") then
		local playerPos = owner.Character.HumanoidRootPart.Position
		local initialPos = playerPos + Vector3.new(3, 2, 0) -- 稍微抬高避免卡在地面
		petModel:SetPrimaryPartCFrame(CFrame.new(initialPos))
		print("🐾 Pet positioned at:", initialPos, "Player at:", playerPos)
	else
		print("⚠️ Could not position pet - player character not found")
	end

	-- 添加調試信息
	print("🐾 Pet model created:", petConfig.name, "for", owner.Name)

	return petModel
end

-- 發送寵物圖鑑數據
function PetService:_sendPetDex(player)
	local playerData = self.DataService:GetPlayerData(player)
	if playerData then
		self.Client.GetPetDex:Fire(player, playerData.pets, playerData.petDex)
	end
end

-- 玩家離開時清理寵物
function PetService:_cleanupPlayerPets(player)
	if activePets[player] then
		activePets[player]:Destroy()
		activePets[player] = nil
	end
end

return PetService
